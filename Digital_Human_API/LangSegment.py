"""
LangSegment compatibility wrapper
"""
import re

class LangSegment:
    @staticmethod
    def setfilters(filters):
        """Compatibility function - does nothing"""
        pass
    
    @staticmethod
    def getTexts(text):
        """Simple text segmentation - returns text as is with language detection"""
        # Simple language detection based on character patterns
        if re.search(r'[\u4e00-\u9fff]', text):
            lang = "zh"  # Chinese
        elif re.search(r'[\u3040-\u309f\u30a0-\u30ff]', text):
            lang = "ja"  # Japanese
        elif re.search(r'[\uac00-\ud7af]', text):
            lang = "ko"  # Korean
        else:
            lang = "en"  # Default to English
        
        return [{"text": text, "lang": lang}]

# For backward compatibility
def setfilters(filters):
    LangSegment.setfilters(filters)

def getTexts(text):
    return LangSegment.getTexts(text)
