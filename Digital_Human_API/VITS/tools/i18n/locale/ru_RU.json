{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Если значение больше 3: применить медианную фильтрацию к вытащенным тональностям. Значение контролирует радиус фильтра и может уменьшить излишнее дыхание.", "A模型权重": "Весы (w) модели А:", "A模型路径": "Путь к модели А:", "B模型路径": "Путь к модели Б:", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Файл дуги F0 (не обязательно). Одна тональность на каждую строчку. Заменяет обычный F0 и модуляцию тональности:", "Index Rate": "Темп индекса", "Onnx导出": "Экспорт ONNX", "Onnx输出路径": "Путь для сохранения модели в формате ONNX:", "RVC模型路径": "Путь к модели RVC:", "ckpt处理": "Обработка ckpt", "harvest进程数": "Количество процессор harvest", "index文件路径不可包含中文": "Путь к файлу индекса", "pth文件路径不可包含中文": "Путь к файлу pth", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Введите номера графических процессоров, разделенные символом «-», например, 0-0-1, чтобы запустить два процесса на GPU 0 и один процесс на GPU 1:", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Шаг 1. Конфигурирование модели. Данные обучения модели сохраняются в папку 'logs', и для каждой модели создаётся отдельная папка. Введите вручную путь к настройкам для модели, в которой находятся логи и тренировочные файлы.", "step1:正在处理数据": "Шаг 1. Переработка данных", "step2:正在提取音高&正在提取特征": "step2:正在提取音高&正在提取特征", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Шаг 2А. Автоматическая обработка исходных аудиозаписей для обучения и выполнение нормализации среза. Создаст 2 папки wav в папке модели. В данный момент поддерживается обучение только на одноголосных записях.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Шаг 2Б. Оценка и извлечение тональности в аудиофайлах с помощью процессора (если включена поддержка изменения высоты звука), извлечение черт с помощью GPU (выберите номер GPU):", "step3: 填写训练设置, 开始训练模型和索引": "Шаг 3. Заполнение дополнительных настроек обучения и запуск обучения модели и индекса", "step3a:正在训练模型": "Шаг 3. Запуск обучения модели", "一键训练": "Обучение в одно нажатие", "也可批量输入音频文件, 二选一, 优先读文件夹": "Можно также импортировать несколько аудиофайлов. Если путь к папке существует, то этот ввод игнорируется.", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Пакетная обработка для разделения вокального сопровождения с использованием модели UVR5.<br>Пример допустимого формата пути к папке: D:\\path\\to\\input\\folder<br> Модель разделена на три категории:<br>1. Сохранить вокал: выберите этот вариант для звука без гармоний. Он сохраняет вокал лучше, чем HP5. Он включает в себя две встроенные модели: HP2 и HP3. HP3 может немного пропускать инструментал, но сохраняет вокал немного лучше, чем HP2.<br>2. Сохранить только основной вокал: выберите этот вариант для звука с гармониями. Это может ослабить основной вокал. Он включает одну встроенную модель: HP5.<br>3. Модели удаления реверберации и задержки (от FoxJoy):<br>  (1) MDX-Net: лучший выбор для удаления стереореверберации, но он не может удалить монореверберацию;<br>&emsp;(234) DeEcho: удаляет эффекты задержки. Агрессивный режим удаляет более тщательно, чем Нормальный режим. DeReverb дополнительно удаляет реверберацию и может удалять монореверберацию, но не очень эффективно для сильно реверберированного высокочастотного контента.<br>Примечания по удалению реверберации/задержки:<br>1. Время обработки для модели DeEcho-DeReverb примерно в два раза больше, чем для двух других моделей DeEcho.<br>2. Модель MDX-Net-Dereverb довольно медленная.<br>3. Рекомендуемая самая чистая конфигурация — сначала применить MDX-Net, а затем DeEcho-Aggressive.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Введите, какие(-ую) GPU(-у) хотите использовать через '-', например 0-1-2, чтобы использовать GPU с номерами 0, 1 и 2:", "伴奏人声分离&去混响&去回声": "Разделение вокала/аккомпанемента и удаление эхо", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Имя файла для сохранения:", "保存的文件名, 默认空为和源文件同名": "Название сохранённого файла (по умолчанию: такое же, как и у входного):", "保存的模型名不带后缀": "Имя файла модели для сохранения (без расширения):", "保存频率save_every_epoch": "Частота сохранения (save_every_epoch):", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Защитить глухие согласные и звуки дыхания для предотвращения артефактов, например, разрывания в электронной музыке. Поставьте на 0.5, чтобы выключить. Уменьшите значение для повышения защиты, но учтите, что при этом может ухудшиться точность индексирования:", "修改": "Изменить", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Изменить информацию о модели (работает только с маленькими моделями, взятыми из папки 'weights')", "停止音频转换": "Закончить конвертацию аудио", "全流程结束！": "Все процессы завершены!", "刷新音色列表和索引路径": "Обновить список голосов и индексов", "加载模型": "Загрузить модель", "加载预训练底模D路径": "Путь к предварительно обученной базовой модели D:", "加载预训练底模G路径": "Путь к предварительно обученной базовой модели G:", "单次推理": "单次推理", "卸载音色省显存": "Выгрузить модель из памяти GPU для освобождения ресурсов", "变调(整数, 半音数量, 升八度12降八度-12)": "Изменить высоту голоса (укажите количество полутонов; чтобы поднять голос на октаву, выберите 12, понизить на октаву — -12):", "后处理重采样至最终采样率，0为不进行重采样": "Изменить частоту дискретизации в выходном файле на финальную. Поставьте 0, чтобы ничего не изменялось:", "否": "Нет", "启用相位声码器": "启用相位声码器", "响应阈值": "Порог ответа", "响度因子": "коэффициент громкости", "处理数据": "Обработать данные", "导出Onnx模型": "Экспортировать модель", "导出文件格式": "Формат выходных файлов", "常见问题解答": "ЧаВо (часто задаваемые вопросы)", "常规设置": "Основные настройки", "开始音频转换": "Начать конвертацию аудио", "很遗憾您这没有能用的显卡来支持您训练": "К сожалению, у вас нету графического процессора, который поддерживает обучение моделей.", "性能设置": "Настройки быстроты", "总训练轮数total_epoch": "Полное количество эпох (total_epoch):", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Массовое преобразование. Введите путь к папке, в которой находятся файлы для преобразования голоса или выгрузите несколько аудиофайлов. Сконвертированные файлы будут сохранены в указанной папке (по умолчанию: 'opt').", "指定输出主人声文件夹": "Путь к папке для сохранения вокала:", "指定输出文件夹": "Папка для результатов:", "指定输出非主人声文件夹": "Путь к папке для сохранения аккомпанемента:", "推理时间(ms):": "Время переработки (мс):", "推理音色": "Желаемый голос:", "提取": "Создать модель", "提取音高和处理数据使用的CPU进程数": "Число процессов ЦП, используемое для оценки высоты голоса и обработки данных:", "是": "Да", "是否仅保存最新的ckpt文件以节省硬盘空间": "Сохранять только последний файл '.ckpt', чтобы сохранить место на диске:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Сохранять маленькую финальную модель в папку 'weights' на каждой точке сохранения:", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Кэшировать все тренировочные сеты в видеопамять. Кэширование маленький датасетов (меньше 10 минут) может ускорить тренировку, но кэширование больших, наоборот, займёт много видеопамяти и не сильно ускорит тренировку:", "显卡信息": "Информация о графических процессорах (GPUs):", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Это программное обеспечение с открытым исходным кодом распространяется по лицензии MIT. Автор никак не контролирует это программное обеспечение. Пользователи, которые используют эту программу и распространяют аудиозаписи, полученные с помощью этой программы, несут полную ответственность за это. Если вы не согласны с этим, вы не можете использовать какие-либо коды и файлы в рамках этой программы или ссылаться на них. Подробнее в файле <b>Agreement-LICENSE.txt</b> в корневом каталоге программы.", "查看": "Просмотреть информацию", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Просмотреть информацию о модели (работает только с маленькими моделями, взятыми из папки 'weights')", "检索特征占比": "Соотношение поиска черт:", "模型": "Модели", "模型推理": "Изменение голоса", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Создание модели из данных, полученных в процессе обучения (введите путь к большому файлу модели в папке 'logs'). Может пригодиться, если вам нужно завершить обучение и получить маленький файл готовой модели, или если вам нужно проверить недообученную модель:", "模型是否带音高指导": "Поддерживает ли модель изменение высоты голоса (1: да, 0: нет):", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Поддержка изменения высоты звука (обязательно для пения, необязательно для речи):", "模型是否带音高指导,1是0否": "Поддерживает ли модель изменение высоты голоса (1: да, 0: нет):", "模型版本型号": "Версия архитектуры модели:", "模型融合, 可用于测试音色融合": "Слияние моделей, может быть использовано для проверки слияния тембра", "模型路径": "Путь к папке:", "每张显卡的batch_size": "Размер пачки для GPU:", "淡入淡出长度": "<PERSON><PERSON><PERSON><PERSON> затухания", "版本": "Версия архитектуры модели:", "特征提取": "Извлечь черты", "特征检索库文件路径,为空则使用下拉的选择结果": "Путь к файлу индекса черт. Оставьте пустым, чтобы использовать выбранный вариант из списка ниже:", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Рекомендуется выбрать +12 для конвертирования мужского голоса в женский и -12 для конвертирования женского в мужской. Если диапазон голоса слишком велик, и голос искажается, можно выбрать значение на свой вкус.", "目标采样率": "Частота дискретизации аудио:", "算法延迟(ms):": "算法延迟(ms):", "自动检测index路径,下拉式选择(dropdown)": "Автоматически найденные файлы индексов черт (выберите вариант из списка):", "融合": "Запустить слияние", "要改的模型信息": "Информация, которая будет изменена:", "要置入的模型信息": "Информация о модели:", "训练": "Обучение модели", "训练模型": "Обучить модель", "训练特征索引": "Обучить индекс черт", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Обучение модели завершено. Журнал обучения можно просмотреть в консоли или в файле 'train.log' в папке с моделью.", "请指定说话人id": "Номер говорящего/поющего:", "请选择index文件": "Пожалуйста, выберите файл индекса", "请选择pth文件": "Пожалуйста, выберите файл pth", "请选择说话人id": "Номер говорящего:", "转换": "Преобразовать", "输入实验名": "Название модели:", "输入待处理音频文件夹路径": "Путь к папке с аудиофайлами для обработки:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Путь к папке с аудиофайлами для переработки (можно скопировать путь из адресной строки файлового менеджера):", "输入待处理音频文件路径(默认是正确格式示例)": "Путь к аудиофайлу, который хотите обработать (ниже указан пример пути к файлу):", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Использовать громкость входного файла для замены или перемешивания с громкостью выходного файла. Чем ближе соотношение к 1, тем больше используется звука из выходного файла:", "输入监听": "输入监听", "输入训练文件夹路径": "Путь к папке с аудиозаписями, на которых будет обучаться модель:", "输入设备": "Входное устройство", "输入降噪": "Уменьшение входного шума", "输出信息": "Статистика", "输出变声": "输出变声", "输出设备": "Выходное устройство", "输出降噪": "Уменьшение выходного шума", "输出音频(右下角三个点,点了可以下载)": "Ауди<PERSON><PERSON><PERSON><PERSON><PERSON> (чтобы скачать, нажмите на три точки справа в плеере)", "选择.index文件": "Выбрать файл .index", "选择.pth文件": "Выбрать файл .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Выберите алгоритм оценки высоты голоса ('pm': работает быстро, но даёт низкое качество речи; 'harvest': басы лучше, но работает очень медленно; 'crepe': лучшее качество, но сильно нагружает GPU; 'rmvpe': лучшее качество и минимальная нагрузка на GPU):", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU", "采样率:": "采样率:", "采样长度": "<PERSON><PERSON><PERSON>на сэмпла", "重载设备列表": "Обновить список устройств", "音调设置": "Настройка высоты звука", "音频设备(请使用同种类驱动)": "Аудиоустройство (пожалуйста, используйте такой же тип драйвера)", "音高算法": "Алгоритм оценки высоты звука", "额外推理时长": "Доп. время переработки"}