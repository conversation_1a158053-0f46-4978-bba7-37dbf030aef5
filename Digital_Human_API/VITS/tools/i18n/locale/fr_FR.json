{">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Si >=3 : appliquer un filtrage médian aux résultats de la reconnaissance de la hauteur de récolte. La valeur représente le rayon du filtre et peut réduire la respiration.", "A模型权重": "Poids (w) pour le modèle A :", "A模型路径": "Chemin d'accès au modèle A :", "B模型路径": "Chemin d'accès au modèle B :", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Fichier de courbe F0 (facultatif). Une hauteur par ligne. Remplace la fréquence fondamentale par défaut et la modulation de la hauteur :", "Index Rate": "Taux d'indexation", "Onnx导出": "Exporter en ONNX", "Onnx输出路径": "Chemin d'exportation ONNX :", "RVC模型路径": "Chemin du modèle RVC :", "ckpt处理": "Traitement des fichiers .ckpt", "harvest进程数": "Nombre de processus CPU utilisés pour l'algorithme de reconnaissance de la hauteur (pitch) dans le cadre de la récolte (harvest).", "index文件路径不可包含中文": "Le chemin du fichier d'index ne doit pas contenir de caractères chinois.", "pth文件路径不可包含中文": "Le chemin du fichier .pth ne doit pas contenir de caractères chinois.", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Configuration des numéros de carte RMVPE : séparez les index GPU par des tirets \"-\", par exemple, 0-0-1 pour utiliser 2 processus sur GPU0 et 1 processus sur GPU1.", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Étape 1 : Remplissez la configuration expérimentale. Les données expérimentales sont stockées dans le dossier 'logs', avec chaque expérience ayant un dossier distinct. Entrez manuellement le chemin du nom de l'expérience, qui contient la configuration expérimentale, les journaux et les fichiers de modèle entraînés.", "step1:正在处理数据": "Étape 1 : Traitement des données en cours.", "step2:正在提取音高&正在提取特征": "Étape 2 : Extraction de la hauteur et extraction des caractéristiques en cours.", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Étape 2a : Parcours automatique de tous les fichiers du dossier d'entraînement qui peuvent être décodés en fichiers audio et réalisation d'une normalisation par tranches. Génère 2 dossiers wav dans le répertoire de l'expérience. Actuellement, seule la formation avec un seul chanteur/locuteur est prise en charge.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Étape 2b : Utilisez le CPU pour extraire la hauteur (si le modèle le permet), utilisez le GPU pour extraire les caractéristiques (sélectionnez l'index du GPU) :", "step3: 填写训练设置, 开始训练模型和索引": "Étape 3 : <PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres d'entraînement et démarrez l'entraînement du modèle ainsi que l'indexation.", "step3a:正在训练模型": "Étape 3a : L'entraînement du modèle a commencé.", "一键训练": "Entraînement en un clic", "也可批量输入音频文件, 二选一, 优先读文件夹": "Il est également possible d'importer plusieurs fichiers audio. Si un chemin de dossier existe, cette entrée est ignorée.", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Traitement en lot pour la séparation de la voix et de l'accompagnement vocal à l'aide du modèle UVR5.<br>Exemple d'un format de chemin de dossier valide : D:\\chemin\\vers\\dossier\\d'entrée (copiez-le depuis la barre d'adresse du gestionnaire de fichiers).<br>Le modèle est divisé en trois catégories :<br>1. Préserver la voix : Choisissez cette option pour l'audio sans harmonies. Elle préserve la voix mieux que HP5. Il comprend deux modèles intégrés : HP2 et HP3. HP3 peut légèrement laisser passer l'accompagnement mais préserve légèrement mieux la voix que HP2.<br>2. Préserver uniquement la voix principale : Choisissez cette option pour l'audio avec harmonies. Cela peut affaiblir la voix principale. Il comprend un modèle intégré : HP5.<br>3. Modèles de suppression de la réverbération et du délai (par FoxJoy) :<br>  (1) MDX-Net : Le meilleur choix pour la suppression de la réverbération stéréo, mais ne peut pas supprimer la réverbération mono.<br>  (234) DeEcho : Supprime les effets de délai. Le mode Aggressive supprime plus efficacement que le mode Normal. DeReverb supprime également la réverbération et peut supprimer la réverbération mono, mais pas très efficacement pour les contenus à haute fréquence fortement réverbérés.<br>Notes sur la suppression de la réverbération et du délai :<br>1. Le temps de traitement pour le modèle DeEcho-DeReverb est environ deux fois plus long que pour les autres deux modèles DeEcho.<br>2. Le modèle MDX-Net-Dereverb est assez lent.<br>3. La configuration la plus propre recommandée est d'appliquer d'abord MDX-Net, puis DeEcho-Aggressive.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Entrez le(s) index GPU séparé(s) par '-', par exemple, 0-1-2 pour utiliser les GPU 0, 1 et 2 :", "伴奏人声分离&去混响&去回声": "Séparation des voix/accompagnement et suppression de la réverbération", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Nom de sauvegarde :", "保存的文件名, 默认空为和源文件同名": "Nom du fichier de sauvegarde (par défaut : identique au nom du fichier source) :", "保存的模型名不带后缀": "Nom du modèle enregistré (sans extension) :", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> de sauvegarde (save_every_epoch) :", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Protéger les consonnes sourdes et les bruits de respiration pour éviter les artefacts tels que le déchirement dans la musique électronique. Réglez à 0,5 pour désactiver. Diminuez la valeur pour renforcer la protection, mais cela peut réduire la précision de l'indexation :", "修改": "Modifier", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modifier les informations du modèle (uniquement pris en charge pour les petits fichiers de modèle extraits du dossier 'weights')", "停止音频转换": "Arrêter la conversion audio", "全流程结束！": "Toutes les étapes ont été terminées !", "刷新音色列表和索引路径": "Actualiser la liste des voix et le vers l'index.", "加载模型": "Charger le modèle.", "加载预训练底模D路径": "Charger le chemin du modèle de base pré-entraîné D :", "加载预训练底模G路径": "Charger le chemin du modèle de base pré-entraîné G :", "单次推理": "单次推理", "卸载音色省显存": "Décharger la voix pour économiser la mémoire GPU.", "变调(整数, 半音数量, 升八度12降八度-12)": "Transposer (entier, nombre de demi-tons, monter d'une octave : 12, descendre d'une octave : -12) :", "后处理重采样至最终采样率，0为不进行重采样": "Rééchantillonner l'audio de sortie en post-traitement à la fréquence d'échantillonnage finale. Réglez sur 0 pour ne pas effectuer de rééchantillonnage :", "否": "Non", "启用相位声码器": "启用相位声码器", "响应阈值": "Seuil de réponse", "响度因子": "Facteur de volume sonore", "处理数据": "Traitement des données", "导出Onnx模型": "Exporter le modèle au format ONNX.", "导出文件格式": "Format de fichier d'exportation", "常见问题解答": "FAQ (Foire Aux Questions)", "常规设置": "Paramètres généraux", "开始音频转换": "Démarrer la conversion audio.", "很遗憾您这没有能用的显卡来支持您训练": "Malheureusement, il n'y a pas de GPU compatible disponible pour prendre en charge votre entrainement.", "性能设置": "Paramètres de performance", "总训练轮数total_epoch": "Nombre total d'époques d'entraînement (total_epoch) :", "批量推理": "批量推理", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversion en lot. Entrez le dossier contenant les fichiers audio à convertir ou téléchargez plusieurs fichiers audio. Les fichiers audio convertis seront enregistrés dans le dossier spécifié (par défaut : 'opt').", "指定输出主人声文件夹": "Spéci<PERSON>z le dossier de sortie pour les fichiers de voix :", "指定输出文件夹": "Spéci<PERSON>z le dossier de sortie :", "指定输出非主人声文件夹": "Spéci<PERSON>z le dossier de sortie pour l'accompagnement :", "推理时间(ms):": "Temps d'inférence (ms) :", "推理音色": "Voix pour l'inférence", "提取": "Extraire", "提取音高和处理数据使用的CPU进程数": "Nombre de processus CPU utilisés pour l'extraction de la hauteur et le traitement des données :", "是": "O<PERSON>", "是否仅保存最新的ckpt文件以节省硬盘空间": "Enregistrer uniquement le dernier fichier '.ckpt' pour économiser de l'espace disque :", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Enregistrer un petit modèle final dans le dossier 'weights' à chaque point de sauvegarde :", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Mettre en cache tous les ensembles d'entrainement dans la mémoire GPU. Mettre en cache de petits ensembles de données (moins de 10 minutes) peut accélérer l'entrainement, mais mettre en cache de grands ensembles de données consommera beaucoup de mémoire GPU et peut ne pas apporter beaucoup d'amélioration de vitesse :", "显卡信息": "Informations sur la carte graphique (GPU)", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Ce logiciel est open source sous la licence MIT. L'auteur n'a aucun contrôle sur le logiciel. Les utilisateurs qui utilisent le logiciel et distribuent les sons exportés par le logiciel en sont entièrement responsables. <br>Si vous n'acceptez pas cette clause, vous ne pouvez pas utiliser ou faire référence à aucun code ni fichier contenu dans le package logiciel. Consultez le fichier <b>Agreement-LICENSE.txt</b> dans le répertoire racine pour plus de détails.", "查看": "Voir", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Afficher les informations sur le modèle (uniquement pour les petits fichiers de modèle extraits du dossier \"weights\")", "检索特征占比": "Rapport de recherche de caractéristiques (contrôle l'intensité de l'accent, un rapport trop élevé provoque des artefacts) :", "模型": "<PERSON><PERSON><PERSON><PERSON>", "模型推理": "Inférence du modèle", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extraction du modèle (saisissez le chemin d'accès au modèle du grand fichier dans le dossier \"logs\"). Cette fonction est utile si vous souhaitez arrêter l'entrainement à mi-chemin et extraire et enregistrer manuellement un petit fichier de modèle, ou si vous souhaitez tester un modèle intermédiaire :", "模型是否带音高指导": "Indique si le modèle dispose d'un guidage en hauteur :", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Indique si le modèle dispose d'un système de guidage de la hauteur (obligatoire pour le chant, facultatif pour la parole) :", "模型是否带音高指导,1是0否": "Le modèle dispose-t-il d'un guide de hauteur (1 : oui, 0 : non) ?", "模型版本型号": "Version de l'architecture du modèle :", "模型融合, 可用于测试音色融合": "Fusion de modèles, peut être utilisée pour tester la fusion de timbres", "模型路径": "Le chemin vers le modèle :", "每张显卡的batch_size": "Taille du batch par GPU :", "淡入淡出长度": "<PERSON><PERSON><PERSON> de la transition", "版本": "Version", "特征提取": "Extraction des caractéristiques", "特征检索库文件路径,为空则使用下拉的选择结果": "Chemin d'accès au fichier d'index des caractéristiques. Laisser vide pour utiliser le résultat sélectionné dans la liste déroulante :", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Il est recommandé d'utiliser la clé +12 pour la conversion homme-femme et la clé -12 pour la conversion femme-homme. Si la plage sonore est trop large et que la voix est déformée, vous pouvez également l'ajuster vous-même à la plage appropriée.", "目标采样率": "Taux d'échantillonnage cible :", "算法延迟(ms):": "Délais algorithmiques (ms):", "自动检测index路径,下拉式选择(dropdown)": "Détecter automatiquement le chemin d'accès à l'index et le sélectionner dans la liste déroulante :", "融合": "Fusion", "要改的模型信息": "Informations sur le modèle à modifier :", "要置入的模型信息": "Informations sur le modèle à placer :", "训练": "<PERSON><PERSON><PERSON><PERSON>", "训练模型": "Entraîner le modèle", "训练特征索引": "Entraîner l'index des caractéristiques", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Entraînement terminé. <PERSON><PERSON> p<PERSON> consulter les rapports d'entraînement dans la console ou dans le fichier 'train.log' situé dans le dossier de l'expérience.", "请指定说话人id": "Veuillez spécifier l'ID de l'orateur ou du chanteur :", "请选择index文件": "Veuillez sélectionner le fichier d'index", "请选择pth文件": "Veuillez sélectionner le fichier pth", "请选择说话人id": "Sélectionner l'ID de l'orateur ou du chanteur :", "转换": "Convertir", "输入实验名": "Saisissez le nom de l'expérience :", "输入待处理音频文件夹路径": "Entrez le chemin du dossier audio à traiter :", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Entrez le chemin du dossier audio à traiter (copiez-le depuis la barre d'adresse du gestionnaire de fichiers) :", "输入待处理音频文件路径(默认是正确格式示例)": "Entrez le chemin d'accès du fichier audio à traiter (par défaut, l'exemple de format correct) :", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Ajustez l'échelle de l'enveloppe de volume. Plus il est proche de 0, plus il imite le volume des voix originales. <PERSON><PERSON> peut aider à masquer les bruits et à rendre le volume plus naturel lorsqu'il est réglé relativement bas. Plus le volume est proche de 1, plus le volume sera fort et constant :", "输入监听": "Moniteur vocal d'entrée", "输入训练文件夹路径": "<PERSON><PERSON><PERSON> le chemin d'accès au dossier d'entraînement :", "输入设备": "Dispositif d'entrée", "输入降噪": "Réduction du bruit d'entrée", "输出信息": "Informations sur la sortie", "输出变声": "Sortie voix convertie", "输出设备": "Dispositif de sortie", "输出降噪": "Ré<PERSON> du bruit de sortie", "输出音频(右下角三个点,点了可以下载)": "Exporter l'audio (cliquer sur les trois points dans le coin inférieur droit pour télécharger)", "选择.index文件": "Sélectionner le fichier .index", "选择.pth文件": "Sélectionner le fichier .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "Sélection de l'algorithme d'extraction de la hauteur, les voix d'entrée peuvent être accélérées avec pm, harvest a de bonnes basses mais est très lent, crepe est bon mais consomme beaucoup de ressources GPU.", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Sélectionnez l'algorithme d'extraction de la hauteur de ton (\"pm\" : extraction plus rapide mais parole de moindre qualité ; \"harvest\" : meilleure basse mais extrêmement lente ; \"crepe\" : meilleure qualité mais utilisation intensive du GPU), \"rmvpe\" : meilleure qualité et peu d'utilisation du GPU.", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Sélection de l'algorithme d'extraction de la hauteur : la chanson d'entrée peut être traitée plus rapidement par pm, avec une voix de haute qualité mais un CPU médiocre, par dio, harvest est meilleur mais plus lent, rmvpe est le meilleur, mais consomme légèrement le CPU/GPU.", "采样率:": "采样率:", "采样长度": "Longueur de l'échantillon", "重载设备列表": "Recharger la liste des dispositifs", "音调设置": "Réglages de la hauteur", "音频设备(请使用同种类驱动)": "Périphérique audio (veuillez utiliser le même type de pilote)", "音高算法": "algorithme de détection de la hauteur", "额外推理时长": "Temps d'inférence supplémentaire"}