{"很遗憾您这没有能用的显卡来支持您训练": "Lamentablemente, no tiene una tarjeta gráfica compatible para admitir su entrenamiento.", "UVR5已开启": "UVR5 está habilitado", "UVR5已关闭": "UVR5 está deshabilitado", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software es de código abierto bajo la licencia MIT. El autor no tiene control sobre el software. El usuario que lo utilice o distribuya, y el que genere sonidos a partir del software, asume toda la responsabilidad. <br>Si no acepta estos términos, no puede utilizar ni hacer referencia a ningún código o archivo dentro del paquete de software. Consulte el archivo <b>LICENSE</b> en el directorio raíz para obtener más detalles.", "0-前置数据集获取工具": "0-Herramienta de obtención de conjunto de datos previo", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Herramienta de separación de voz y acompañamiento UVR5 y eliminación de reverberación y retardo", "是否开启UVR5-WebUI": "¿Habilitar UVR5-WebUI?", "UVR5进程输出信息": "Información de salida del proceso UVR5", "0b-语音切分工具": "0b-Herramienta de división de voz", ".list标注文件的路径": "Ruta del archivo de anotación .list", "GPT模型列表": "Lista de modelos GPT", "SoVITS模型列表": "Lista de modelos SoVITS", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "Directorio donde se guardan los archivos de audio después del corte! Ruta completa del archivo de audio a leer = este directorio - nombre de archivo correspondiente a la forma de onda en el archivo de lista (no la ruta completa).", "音频自动切分输入路径，可文件可文件夹": "Ruta de entrada para la división automática de audio, puede ser un archivo o una carpeta", "切分后的子音频的输出根目录": "Directorio raíz de salida de los sub-audios después de la división", "怎么切": "Cómo cortar", "不切": "No cortar", "凑四句一切": "Completa cuatro oraciones para rellenar todo", "按英文句号.切": "Cortar por puntos en inglés.", "threshold:音量小于这个值视作静音的备选切割点": "umbral: puntos de corte alternativos considerados como silencio si el volumen es menor que este valor", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: duración mínima de cada segmento, si el primer segmento es demasiado corto, se conecta continuamente con los siguientes hasta que supera este valor", "min_interval:最短切割间隔": "min_interval: intervalo mínimo de corte", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: cómo calcular la curva de volumen, cuanto más pequeño, mayor precisión pero mayor carga computacional (mayor precisión no significa mejor rendimiento)", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: duración máxima del silencio después del corte", "开启语音切割": "Habilitar la división de voz", "终止语音切割": "Terminar la división de voz", "max:归一化后最大值多少": "max: valor máximo después de la normalización", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proporción de mezcla de audio normalizado que entra", "切割使用的进程数": "Número de procesos utilizados para la división", "语音切割进程输出信息": "Información de salida del proceso de división de voz", "0c-中文批量离线ASR工具": "0c-Herramienta de ASR en lote fuera de línea en chino", "开启离线批量ASR": "¿Habilitar ASR en lote fuera de línea?", "终止ASR进程": "Terminar el proceso ASR", "批量ASR(中文only)输入文件夹路径": "<PERSON>uta de la carpeta de entrada para ASR en lote (solo en chino)", "ASR进程输出信息": "Información de salida del proceso ASR", "0d-语音文本校对标注工具": "0d-Herramienta de corrección y etiquetado de texto de voz", "是否开启打标WebUI": "¿Habilitar la interfaz web de etiquetado?", "打标数据标注文件路径": "Ruta del archivo de etiquetado de datos", "打标工具进程输出信息": "Información de salida del proceso de la herramienta de etiquetado", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*Nombre del experimento/modelo", "显卡信息": "Información de la tarjeta gráfica", "预训练的SoVITS-G模型路径": "Ruta del modelo SoVITS-G preentrenado", "预训练的SoVITS-D模型路径": "Ruta del modelo SoVITS-D preentrenado", "预训练的GPT模型路径": "Ruta del modelo GPT preentrenado", "1A-训练集格式化工具": "1A-Herramienta de formateo del conjunto de datos de entrenamiento", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Debe haber archivos y carpetas que comiencen con 23456 en el directorio logs/nombre del experimento", "*文本标注文件": "*Archivo de etiquetado de texto", "*训练集音频文件目录": "*Directorio de archivos de audio de entrenamiento", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "Directorio de archivos de audio de entrenamiento, concatenar con los nombres de archivo correspondientes en el archivo list.", "1Aa-文本内容": "1Aa-Contenido del texto", "GPU卡号以-分割，每个卡号一个进程": "Número de tarjeta GPU separado por '-', cada número de tarjeta es un proceso", "预训练的中文BERT模型路径": "Ruta del modelo BERT en chino preentrenado", "开启文本获取": "¿Habilitar la obtención de texto?", "终止文本获取进程": "Terminar el proceso de obtención de texto", "文本进程输出信息": "Información de salida del proceso de obtención de texto", "1Ab-SSL自监督特征提取": "1Ab-Extracción de características auto-supervisada SSL", "预训练的SSL模型路径": "Ruta del modelo SSL preentrenado", "开启SSL提取": "¿Habilitar la extracción SSL?", "终止SSL提取进程": "Terminar el proceso de extracción SSL", "SSL进程输出信息": "Información de salida del proceso SSL", "1Ac-语义token提取": "1Ac-Extracción de tokens semánticos", "开启语义token提取": "¿Habilitar la extracción de tokens semánticos?", "终止语义token提取进程": "Terminar el proceso de extracción de tokens semánticos", "语义token提取进程输出信息": "Información de salida del proceso de extracción de tokens semánticos", "1Aabc-训练集格式化一键三连": "1Aabc-Formateo del conjunto de datos de entrenamiento en un solo paso", "开启一键三连": "¿Habilitar un solo paso de formateo?", "终止一键三连": "Terminar el proceso de un solo paso de formateo", "一键三连进程输出信息": "Información de salida del proceso de triple acción", "1B-微调训练": "1B-Entrenamiento de ajuste fino", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Entrenamiento de SoVITS. Los archivos de modelo para compartir se encuentran en SoVITS_weights.", "每张显卡的batch_size": "Tamaño de lote por tarjeta gráfica", "总训练轮数total_epoch，不建议太高": "Número total de épocas de entrenamiento, no se recomienda demasiado alto", "文本模块学习率权重": "Peso de la tasa de aprendizaje del módulo de texto", "保存频率save_every_epoch": "Frecuencia de guardado (cada epoch)", "是否仅保存最新的ckpt文件以节省硬盘空间": "¿Guardar solo el último archivo ckpt para ahorrar espacio en disco?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "¿Guardar el modelo final pequeño en la carpeta de pesos en cada punto de guardado?", "开启SoVITS训练": "Iniciar entrenamiento de SoVITS", "终止SoVITS训练": "Detener entrenamiento de SoVITS", "SoVITS训练进程输出信息": "Información de salida del proceso de entrenamiento de SoVITS", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Entrenamiento de GPT. Los archivos de modelo para compartir se encuentran en GPT_weights.", "总训练轮数total_epoch": "Número total de épocas de entrenamiento", "开启GPT训练": "Iniciar entrenamiento de GPT", "终止GPT训练": "Detener entrenamiento de GPT", "GPT训练进程输出信息": "Información de salida del proceso de entrenamiento de GPT", "1C-推理": "1C-Inferencia", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Seleccione el modelo almacenado en SoVITS_weights y GPT_weights después del entrenamiento. Uno de ellos es el modelo base, útil para experimentar con TTS de 5 segundos sin entrenamiento.", "*GPT模型列表": "*Lista de modelos GPT", "*SoVITS模型列表": "*Lista de modelos SoVITS", "GPU卡号,只能填1个整数": "Número de tarjeta GPU, solo se puede ingresar un número entero", "刷新模型路径": "Actualizar la ruta del modelo", "是否开启TTS推理WebUI": "¿Habilitar la interfaz web de inferencia TTS?", "TTS推理WebUI进程输出信息": "Información de salida del proceso de interfaz web de inferencia TTS", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Cambio de voz", "施工中，请静候佳音": "En construcción, por favor espere pacientemente", "TTS推理进程已开启": "Proceso de inferencia TTS iniciado", "TTS推理进程已关闭": "Proceso de inferencia TTS cerrado", "打标工具WebUI已开启": "Interfaz web de la herramienta de etiquetado iniciada", "打标工具WebUI已关闭": "Interfaz web de la herramienta de etiquetado cerrada", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Este software es de código abierto bajo la licencia MIT. El autor no tiene control sobre el software. El usuario que lo utilice o distribuya, y el que genere sonidos a partir del software, asume toda la responsabilidad. Si no acepta estos términos, no puede utilizar ni hacer referencia a ningún código o archivo dentro del paquete de software. Consulte el archivo LICENSE en el directorio raíz para obtener más detalles.", "*请上传并填写参考信息": "*Por favor, suba y complete la información de referencia", "*请填写需要合成的目标文本": "*Por favor, complete el texto objetivo que necesita ser sintetizado", "ASR任务开启：%s": "Tarea ASR iniciada: %s", "GPT训练完成": "Entrenamiento de GPT completado", "GPT训练开始：%s": "Entrenamiento de GPT iniciado: %s", "SSL提取进程执行中": "Proceso de extracción SSL en ejecución", "SSL提取进程结束": "Proceso de extracción SSL finalizado", "SoVITS训练完成": "Entrenamiento de SoVITS completado", "SoVITS训练开始：%s": "Entrenamiento de SoVITS iniciado: %s", "一键三连中途报错": "Error intermedio en triple acción", "一键三连进程结束": "Proceso de triple acción finalizado", "中文": "Chino", "凑50字一切": "Todo para alcanzar las 50 palabras", "凑五句一切": "Todo para alcanzar las cinco frases", "切分后文本": "Texto después de la división", "切割执行中": "División en proceso", "切割结束": "División finalizada", "参考音频的文本": "Texto de referencia del audio", "参考音频的语种": "Idioma del audio de referencia", "合成语音": "Sín<PERSON>is de voz", "后续将支持混合语种编码文本输入。": "En el futuro, se admitirá la entrada de texto con codificación de idiomas mixtos.", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "Ya hay una tarea ASR en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "Ya hay una tarea de entrenamiento de GPT en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "Ya hay una tarea de extracción SSL en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "Ya hay una tarea de entrenamiento de SoVITS en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "Ya hay una tarea de triple acción en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的切割任务，需先终止才能开启下一次任务": "Ya hay una tarea de división en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的文本任务，需先终止才能开启下一次任务": "Ya hay una tarea de texto en curso, debe detenerla antes de comenzar la siguiente tarea", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "Ya hay una tarea de extracción de tokens semánticos en curso, debe detenerla antes de comenzar la siguiente tarea", "已终止ASR进程": "Proceso ASR terminado", "已终止GPT训练": "Entrenamiento de GPT terminado", "已终止SoVITS训练": "Entrenamiento de SoVITS terminado", "已终止所有1a进程": "Se han terminado todos los procesos 1a", "已终止所有1b进程": "Se han terminado todos los procesos 1b", "已终止所有一键三连进程": "Se han terminado todos los procesos de triple acción", "已终止所有切割进程": "Proceso de corte terminado", "已终止所有语义token进程": "Proceso de extracción de tokens semánticos terminado", "按中文句号。切": "Cortar según puntos en chino", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Herramienta de división de texto. El resultado de la síntesis puede no ser bueno para textos demasiado largos, por lo que se recomienda dividirlos primero. La síntesis se realiza separando el texto según los saltos de línea y luego uniendo los fragmentos.", "文本进程执行中": "Proceso de texto en ejecución", "文本进程结束": "Proceso de texto finalizado", "日文": "Japonés", "英文": "Inglés", "语义token提取进程执行中": "Proceso de extracción de tokens semánticos en ejecución", "语义token提取进程结束": "Proceso de extracción de tokens semánticos finalizado", "请上传参考音频": "Por favor, suba el audio de referencia", "输入路径不存在": "La ruta de entrada no existe", "输入路径存在但既不是文件也不是文件夹": "La ruta de entrada existe pero no es ni un archivo ni una carpeta", "输出的语音": "Audio de salida", "进度：1a-done": "Progreso: 1a-hecho", "进度：1a-done, 1b-ing": "Progreso: 1a-hecho, 1b-en proceso", "进度：1a-ing": "Progreso: 1a-en proceso", "进度：1a1b-done": "Progreso: 1a1b-hecho", "进度：1a1b-done, 1cing": "Progreso: 1a1b-hecho, 1c-en proceso", "进度：all-done": "Progreso: todo hecho", "需要合成的切分前文本": "Texto a sintetizar antes de la división", "需要合成的文本": "Texto a sintetizar", "需要合成的语种": "Idioma para la síntesis", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Si es >=3, se utiliza la mediana para filtrar los resultados del reconocimiento de altura tonal de harvest, el valor es el radio del filtro. Su uso puede debilitar los sonidos sordos.", "A模型权重": "Peso del modelo A", "A模型路径": "Ruta del modelo A", "B模型路径": "Ruta del modelo B", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Archivo de curva F0, opcional, una línea por altura tonal, en lugar de F0 y cambio de tono predeterminados", "Index Rate": "<PERSON><PERSON>", "Onnx导出": "Exportar a Onnx", "Onnx输出路径": "<PERSON>uta de salida de <PERSON>nx", "RVC模型路径": "Ruta del modelo RVC", "ckpt处理": "Procesamiento de ckpt", "harvest进程数": "Número de procesos de harvest", "index文件路径不可包含中文": "La ruta del archivo de índice no puede contener caracteres chinos", "pth文件路径不可包含中文": "La ruta del archivo pth no puede contener caracteres chinos", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Configuración de números de tarjeta rmvpe: usando - para separar los números de tarjeta de diferentes procesos de entrada, por ejemplo, 0-0-1 para ejecutar 2 procesos en la tarjeta 0 y 1 proceso en la tarjeta 1", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Paso 1: Completa la configuración del experimento. Los datos del experimento se encuentran en logs, cada experimento en una carpeta, debe ingresar manualmente la ruta del nombre del experimento, que incluye la configuración del experimento, el registro y los archivos del modelo entrenado.", "step1:正在处理数据": "Paso 1: <PERSON><PERSON><PERSON><PERSON>", "step2:正在提取音高&正在提取特征": "Paso 2: Extrayendo tono y características", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Paso 2a: Recorre automáticamente todos los archivos en la carpeta de entrenamiento que se pueden decodificar en archivos de audio y realiza la normalización de segmentos. Genera 2 carpetas de audio en el directorio del experimento; por ahora, solo es compatible con el entrenamiento de una sola persona.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Paso 2b: Extraer tono con CPU (si el modelo incluye tono) y extraer características con GPU (seleccionar número de tarjeta)", "step3: 填写训练设置, 开始训练模型和索引": "Paso 3: Completa la configuración de entrenamiento y comienza a entrenar el modelo e indexar", "step3a:正在训练模型": "Paso 3a: <PERSON><PERSON><PERSON><PERSON> el modelo", "一键训练": "Entrenamiento con un clic", "也可批量输入音频文件, 二选一, 优先读文件夹": "También se pueden ingresar archivos de audio por lotes, seleccionar uno, prioridad para leer carpetas", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Usar - para separar los números de tarjeta utilizados como entrada, por ejemplo, 0-1-2 para usar las tarjetas 0, 1 y 2", "伴奏人声分离&去混响&去回声": "Separación de acompañamiento y voz principal y eliminación de reverberación y eco", "使用模型采样率": "Usar tasa de muestreo del modelo", "使用设备采样率": "Usar tasa de muestreo del dispositivo", "保存名": "Nombre de guardado", "保存的文件名, 默认空为和源文件同名": "Nombre de archivo guardado, vacío por defecto para tener el mismo nombre que el archivo fuente", "保存的模型名不带后缀": "Nombre del modelo guardado sin extensión", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Proteger las consonantes claras y los sonidos de respiración, evitando artefactos como el desgarro eléctrico. No activar al tirar hasta 0.5, reducir para aumentar la protección, pero puede disminuir la efectividad del índice", "修改": "Modificar", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modificar información del modelo (solo compatible con archivos de modelo pequeños extraídos en la carpeta weights)", "停止音频转换": "Detener la conversión de audio", "全流程结束！": "¡Proceso completo!", "刷新音色列表和索引路径": "Actualizar lista de tonos e índice de ruta", "加载模型": "<PERSON><PERSON> modelo", "加载预训练底模D路径": "Cargar ruta del modelo D preentrenado", "加载预训练底模G路径": "Cargar ruta del modelo G preentrenado", "单次推理": "Inferencia única", "卸载音色省显存": "<PERSON><PERSON><PERSON> tono para ahorrar memoria de video", "变调(整数, 半音数量, 升八度12降八度-12)": "Cambiar tono (número entero, cantidad de semitonos, subir octava 12 bajar octava -12)", "后处理重采样至最终采样率，0为不进行重采样": "Reprocesar y remuestrear a la tasa de muestreo final, 0 para no remuestrear", "否": "No", "启用相位声码器": "Activar codificador de fase", "响应阈值": "Umbral de respuesta", "响度因子": "Factor de sonoridad", "处理数据": "Procesar da<PERSON>", "导出Onnx模型": "Exportar modelo Onnx", "导出文件格式": "Formato de archivo de exportación", "常见问题解答": "Preguntas frecuentes", "常规设置": "Configuración general", "开始音频转换": "Iniciar conversión de audio", "性能设置": "Configuración de rendimiento", "批量推理": "Inferencia por lotes", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversión por lotes, ingrese la carpeta de audio a convertir o cargue varios archivos de audio, la salida se realiza en la carpeta especificada (opt por defecto). ", "指定输出主人声文件夹": "Especificar carpeta de salida de voz principal", "指定输出文件夹": "Especificar carpeta de salida", "指定输出非主人声文件夹": "Especificar carpeta de salida de no voz principal", "推理时间(ms):": "Tiempo de inferencia (ms):", "推理音色": "Tono de inferencia", "提取": "Extraer", "提取音高和处理数据使用的CPU进程数": "Número de procesadores de CPU utilizados para extraer tono y procesar datos", "是": "Sí", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Almacenar en caché todos los conjuntos de entrenamiento en la memoria de video. Pequeños conjuntos de datos menores a 10 minutos pueden almacenarse en caché para acelerar el entrenamiento; almacenar en caché grandes conjuntos de datos puede saturar la memoria de video y no acelerará mucho.", "查看": "<PERSON>er", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Ver información del modelo (solo compatible con archivos pequeños extraídos en la carpeta weights)", "检索特征占比": "Proporción de características de búsqueda", "模型": "<PERSON><PERSON>", "模型推理": "Inferencia de modelo", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extracción de modelo (ingresar la ruta del modelo grande en la carpeta logs), útil cuando se quiere dejar de entrenar a la mitad y el modelo no ha extraído automáticamente un modelo pequeño guardado, o para probar la situación del modelo intermedio", "模型是否带音高指导": "¿El modelo incluye guía de altura tonal?", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "¿El modelo incluye guía de altura tonal? (Necesario para cantar, opcional para voz)", "模型是否带音高指导,1是0否": "¿El modelo incluye guía de altura tonal? 1 para sí, 0 para no", "模型版本型号": "Versión y modelo del modelo", "模型融合, 可用于测试音色融合": "Fusión de modelos, útil para probar la mezcla de tonos", "模型路径": "Ruta del modelo", "淡入淡出长度": "<PERSON><PERSON><PERSON> de desvanecimiento", "版本": "Versión", "特征提取": "Extracción de características", "特征检索库文件路径,为空则使用下拉的选择结果": "Ruta del archivo de la biblioteca de búsqueda de características, si está vacío, se utiliza el resultado seleccionado en el menú desplegable", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Recomendación para cambiar de hombre a mujer +12 teclas, cambiar de mujer a hombre -12 teclas. Si la amplitud del rango tonal causa distorsión del tono, también puede ajustarse manualmente al rango tonal adecuado. ", "目标采样率": "Tasa de muestreo objetivo", "算法延迟(ms):": "Retar<PERSON> del algoritmo (ms):", "自动检测index路径,下拉式选择(dropdown)": "Detectar automáticamente la ruta del índice, seleccionar en menú desplegable", "融合": "Fusión", "要改的模型信息": "Información del modelo a cambiar", "要置入的模型信息": "Información del modelo a insertar", "训练": "Entrenar", "训练模型": "Entrenar modelo", "训练特征索引": "Entrenar índice de características", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Entrenamiento terminado, puede ver registros de entrenamiento en la consola o en el archivo train.log en la carpeta del experimento", "请指定说话人id": "Por favor, especifique el ID del hablante", "请选择index文件": "Seleccione el archivo index, por favor", "请选择pth文件": "Seleccione el archivo pth, por favor", "请选择说话人id": "Seleccione el ID del hablante, por favor", "转换": "Convertir", "输入实验名": "Ingrese el nombre del experimento", "输入待处理音频文件夹路径": "Ingrese la ruta de la carpeta de audio a procesar", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Ingrese la ruta de la carpeta de audio a procesar (puede copiarla desde la barra de direcciones del administrador de archivos)", "输入待处理音频文件路径(默认是正确格式示例)": "Ingrese la ruta del archivo de audio a procesar (el formato predeterminado es un ejemplo correcto)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Ingrese la proporción de fusión para reemplazar el sobre de volumen de origen con el sobre de volumen de salida; cuanto más cercano a 1, más se utiliza el sobre de salida", "输入监听": "Entrada de monitoreo", "输入训练文件夹路径": "Ingrese la ruta de la carpeta de entrenamiento", "输入设备": "Dispositivo de entrada", "输入降噪": "Entrada de reducción de ruido", "输出信息": "Información de salida", "输出变声": "Salida de cambio de voz", "输出设备": "Dispositivo de salida", "输出降噪": "Salida de reducción de ruido", "输出音频(右下角三个点,点了可以下载)": "Salida de audio (los tres puntos en la esquina inferior derecha, haga clic para descargar)", "选择.index文件": "Seleccione el archivo .index, por favor", "选择.pth文件": "Seleccione el archivo .pth, por favor", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "Seleccione el algoritmo de extracción de tono; para voz, pm acelera, harvest es lento pero tiene buenos bajos, crepe tiene buen efecto pero consume GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Seleccione el algoritmo de extracción de tono; para voz, pm acelera, harvest es lento pero tiene buenos bajos, crepe tiene buen efecto pero consume GPU, rmvpe tiene el mejor efecto y consume poco GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Seleccione el algoritmo de extracción de tono: para voz, pm acelera con buena calidad de audio pero CPU deficiente, dio acelera pero harvest tiene mejor calidad aunque es más lento, rmvpe tiene el mejor efecto y consume poco CPU/GPU", "采样率:": "Tasa de muestreo:", "采样长度": "Longitud de muestreo", "重载设备列表": "Recargar lista de dispositivos", "音调设置": "Configuración de tono", "音频设备(请使用同种类驱动)": "Dispositivo de audio (utilice controladores del mismo tipo)", "音高算法": "Algoritmo de tono", "额外推理时长": "Tiempo adicional de inferencia"}