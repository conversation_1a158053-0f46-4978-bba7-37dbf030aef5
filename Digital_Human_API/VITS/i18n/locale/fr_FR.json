{"很遗憾您这没有能用的显卡来支持您训练": "Malheureusement, votre carte graphique n'est pas compatible avec l'entraînement.", "UVR5已开启": "UVR5 est activé", "UVR5已关闭": "UVR5 est désactivé", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Ce logiciel est open source sous la licence MIT. L'auteur n'a aucun contrôle sur le logiciel. Les utilisateurs et les diffuseurs du son exporté par le logiciel en assument l'entière responsabilité. <br>Si vous n'acceptez pas ces termes, vous ne pouvez ni utiliser ni citer aucun code ou fichier à l'intérieur du package. Voir <b>LICENSE</b> dans le répertoire racine pour plus de détails.", "0-前置数据集获取工具": "0-Outil de récupération de jeu de données préalable", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Outil de séparation de la voix humaine et de l'accompagnement UVR5 & suppression de la réverbération et du retard", "是否开启UVR5-WebUI": "Activer UVR5-WebUI", "UVR5进程输出信息": "Informations de processus UVR5", "0b-语音切分工具": "0b-<PERSON><PERSON> vocal", ".list标注文件的路径": "Chemin du fichier d'annotation .list", "GPT模型列表": "Liste des modèles GPT", "SoVITS模型列表": "Liste des modèles SoVITS", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "Répertoire où sont enregistrés les fichiers audio après la découpe ! Chemin complet du fichier audio à lire = ce répertoire - nom du fichier correspondant à la forme d'onde dans le fichier liste (pas le chemin complet).", "音频自动切分输入路径，可文件可文件夹": "Chemin d'entrée automatique de découpage audio, peut être un fichier ou un dossier", "切分后的子音频的输出根目录": "Répertoire racine de sortie des sous-audios après découpage", "怎么切": "Comment d<PERSON>", "不切": "Pas de découpe", "凑四句一切": "Composez quatre phrases pour tout remplir", "按英文句号.切": "Découpez par des points en anglais", "threshold:音量小于这个值视作静音的备选切割点": "seuil: le volume inférieur à cette valeur est considéré comme un point de coupe silencieux alternatif", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: longueur minimale de chaque segment, si le premier segment est trop court, il est continué avec le segment suivant jusqu'à dépasser cette valeur", "min_interval:最短切割间隔": "min_interval: intervalle de coupe minimum", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: comment calculer la courbe de volume, plus petit pour une précision plus élevée mais une charge de calcul plus élevée (ce n'est pas une meilleure précision)", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: durée maximale de silence après la coupe", "开启语音切割": "<PERSON><PERSON> le dé<PERSON>upage vocal", "终止语音切割": "<PERSON><PERSON><PERSON><PERSON> le dé<PERSON>upage vocal", "max:归一化后最大值多少": "max: valeur maximale après normalisation", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proportion d'audio normalisé mélangé", "切割使用的进程数": "Nombre de processus utilisés pour le découpage", "语音切割进程输出信息": "Informations de processus de découpage vocal", "0c-中文批量离线ASR工具": "0c-Outil chinois de transcription automatique hors ligne en masse", "开启离线批量ASR": "Activer la transcription automatique hors ligne en masse", "终止ASR进程": "Arrêter le processus ASR", "批量ASR(中文only)输入文件夹路径": "Chemin du dossier d'entrée pour la transcription automatique hors ligne en masse (chinois uniquement)", "ASR进程输出信息": "Informations de processus ASR", "0d-语音文本校对标注工具": "0d-Outil de correction et d'annotation de texte vocal", "是否开启打标WebUI": "Activer l'interface Web d'annotation", "打标数据标注文件路径": "Chemin du fichier d'annotation des données annotées", "打标工具进程输出信息": "Informations de processus de l'outil d'annotation", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*Nom de l'expérience/modèle", "显卡信息": "Informations sur la carte graphique", "预训练的SoVITS-G模型路径": "Chemin du modèle SoVITS-G pré-entraîné", "预训练的SoVITS-D模型路径": "Chemin du modèle SoVITS-D pré-entraîné", "预训练的GPT模型路径": "Chemin du modèle GPT pré-entraîné", "1A-训练集格式化工具": "1A-Outil de formatage du jeu de données d'entraînement", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Les fichiers et dossiers commençant par 23456 devraient être présents dans le répertoire logs/nom de l'expérience", "*文本标注文件": "*Fichier d'annotation de texte", "*训练集音频文件目录": "*Répertoire des fichiers audio d'entraînement", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "Répertoire des fichiers audio d'entraînement - concaténer avec les noms de fichiers correspondants dans le fichier de liste", "1Aa-文本内容": "1Aa-Contenu du texte", "GPU卡号以-分割，每个卡号一个进程": "Numéro de carte GPU séparé par des tirets, un processus par numéro de carte", "预训练的中文BERT模型路径": "Chemin du modèle BERT chinois pré-entraîné", "开启文本获取": "Activer l'extraction de texte", "终止文本获取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction de texte", "文本进程输出信息": "Informations de processus de texte", "1Ab-SSL自监督特征提取": "1Ab-Extraction de caractéristiques auto-supervisée SSL", "预训练的SSL模型路径": "Chemin du modèle SSL pré-entraîné", "开启SSL提取": "Activer l'extraction SSL", "终止SSL提取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction SSL", "SSL进程输出信息": "Informations de processus SSL", "1Ac-语义token提取": "1Ac-Extraction de jetons sémantiques", "开启语义token提取": "Activer l'extraction de jetons sémantiques", "终止语义token提取进程": "<PERSON><PERSON><PERSON><PERSON> le processus d'extraction de jetons sémantiques", "语义token提取进程输出信息": "Informations de processus d'extraction de jetons sémantiques", "1Aabc-训练集格式化一键三连": "1Aabc-Formatage en un clic du jeu de données d'entraînement", "开启一键三连": "Activer l'un clic trois connexions", "终止一键三连": "Arr<PERSON><PERSON> l'un clic trois connexions", "一键三连进程输出信息": "Informations de processus de l'un clic trois connexions", "1B-微调训练": "1B-Entraînement fin", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Entraînement SoVITS. Les fichiers de modèle destinés au partage sont enregistrés sous SoVITS_weights.", "每张显卡的batch_size": "Taille de lot par carte graphique", "总训练轮数total_epoch，不建议太高": "Nombre total d'époques d'entraînement, pas recommandé d'être trop élevé", "文本模块学习率权重": "Poids du taux d'apprentissage du module de texte", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> de sauvegarde (sauvegarder à chaque époque)", "是否仅保存最新的ckpt文件以节省硬盘空间": "Sauvegarder uniquement le dernier fichier ckpt pour économiser de l'espace disque", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Sauvegarder le petit modèle final dans le dossier weights à chaque point de sauvegarde", "开启SoVITS训练": "Activer l'entraînement SoVITS", "终止SoVITS训练": "Arrêter l'entraînement SoVITS", "SoVITS训练进程输出信息": "Informations de processus d'entraînement SoVITS", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Entraînement GPT. Les fichiers de modèle destinés au partage sont enregistrés sous GPT_weights.", "总训练轮数total_epoch": "Nombre total d'époques d'entraînement", "开启GPT训练": "Activer l'entraînement GPT", "终止GPT训练": "Arrêter l'entraînement GPT", "GPT训练进程输出信息": "Informations de processus d'entraînement GPT", "1C-推理": "1C-Inférence", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Choisissez le modèle entraîné stocké sous SoVITS_weights et GPT_weights. Par d<PERSON><PERSON><PERSON>, l'un d'eux est un modèle de base pour l'expérience de TTS Zero Shot de 5 secondes.", "*GPT模型列表": "*Liste des modèles GPT", "*SoVITS模型列表": "*Liste des modèles SoVITS", "GPU卡号,只能填1个整数": "Numéro de carte GPU, ne peut contenir qu'un seul entier", "刷新模型路径": "Actualiser le chemin du modèle", "是否开启TTS推理WebUI": "Activer l'interface Web d'inférence TTS", "TTS推理WebUI进程输出信息": "Informations de processus de l'interface Web d'inférence TTS", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Modification de la voix", "施工中，请静候佳音": "En construction, veuillez attendre patiemment", "TTS推理进程已开启": "Le processus d'inférence TTS est en cours", "TTS推理进程已关闭": "Le processus d'inférence TTS est terminé", "打标工具WebUI已开启": "L'interface Web de l'outil d'annotation est en cours", "打标工具WebUI已关闭": "L'interface Web de l'outil d'annotation est terminée", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Ce logiciel est open source sous la licence MIT. L'auteur n'a aucun contrôle sur le logiciel. Les utilisateurs et les diffuseurs du son exporté par le logiciel en assument l'entière responsabilité. Si vous n'acceptez pas ces termes, vous ne pouvez ni utiliser ni citer aucun code ou fichier à l'intérieur du package. Voir LICENSE dans le répertoire racine pour plus de détails.", "*请上传并填写参考信息": "*Veuillez télécharger et remplir les informations de référence", "*请填写需要合成的目标文本": "*Veuillez remplir le texte cible à synthétiser", "ASR任务开启：%s": "Tâche ASR activée : %s", "GPT训练完成": "Entraînement GPT terminé", "GPT训练开始：%s": "Entraînement GPT commencé : %s", "SSL提取进程执行中": "Processus d'extraction SSL en cours", "SSL提取进程结束": "Processus d'extraction SSL terminé", "SoVITS训练完成": "Entraînement SoVITS terminé", "SoVITS训练开始：%s": "Entraînement SoVITS commencé : %s", "一键三连中途报错": "Erreur intermédiaire dans la séquence d'un clic trois connexions", "一键三连进程结束": "Processus de séquence d'un clic trois connexions terminé", "中文": "<PERSON><PERSON>", "凑50字一切": "Assembler 50 mots tout", "凑五句一切": "Assembler cinq phrases tout", "切分后文本": "Texte après d<PERSON>", "切割执行中": "Découpage en cours", "切割结束": "Découpage terminé", "参考音频的文本": "Texte de l'audio de référence", "参考音频的语种": "Langue de l'audio de référence", "合成语音": "Synthèse vocale", "后续将支持混合语种编码文本输入。": "Prise en charge ultérieure du codage de texte avec des langues mixtes.", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "Une tâche ASR est déjà en cours. V<PERSON> de<PERSON> d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "Une tâche d'entraînement GPT est déjà en cours. <PERSON><PERSON> de<PERSON> d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "Une tâche d'extraction SSL est déjà en cours. <PERSON><PERSON> de<PERSON> d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "Une tâche d'entraînement SoVITS est déjà en cours. V<PERSON> devez d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "Une tâche d'une séquence d'un clic trois connexions est déjà en cours. V<PERSON> devez d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的切割任务，需先终止才能开启下一次任务": "Une tâche de découpage est déjà en cours. <PERSON><PERSON> de<PERSON> d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的文本任务，需先终止才能开启下一次任务": "Une tâche de texte est déjà en cours. V<PERSON> de<PERSON> d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "Une tâche d'extraction de jetons sémantiques est déjà en cours. V<PERSON> devez d'abord l'arrêter avant de démarrer une nouvelle tâche.", "已终止ASR进程": "Processus ASR arrêté", "已终止GPT训练": "Entraînement GPT arrêté", "已终止SoVITS训练": "Entraînement SoVITS arrêté", "已终止所有1a进程": "Tous les processus 1a ont été arrêtés", "已终止所有1b进程": "Tous les processus 1b ont été arrêtés", "已终止所有一键三连进程": "Tous les processus d'une séquence d'un clic trois connexions ont été arrêtés", "已终止所有切割进程": "Tous les processus de découpage ont été arrêtés", "已终止所有语义token进程": "Tous les processus de jetons sémantiques ont été arrêtés", "按中文句号。切": "Couper selon les points en chinois.", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Outil de découpage de texte. Un texte trop long peut ne pas donner un bon résultat, donc il est recommandé de le couper d'abord s'il est trop long. La synthèse se fera en séparant le texte par les sauts de ligne puis en les assemblant.", "文本进程执行中": "Processus de texte en cours", "文本进程结束": "Processus de texte terminé", "日文": "Japonais", "英文": "<PERSON><PERSON><PERSON>", "语义token提取进程执行中": "Processus d'extraction de jetons sémantiques en cours", "语义token提取进程结束": "Processus d'extraction de jetons sémantiques terminé", "请上传参考音频": "Veuillez télécharger l'audio de référence", "输入路径不存在": "Le chemin d'entrée n'existe pas", "输入路径存在但既不是文件也不是文件夹": "Le chemin d'entrée existe mais n'est ni un fichier ni un dossier", "输出的语音": "Audio de sortie", "进度：1a-done": "Progression : 1a-done", "进度：1a-done, 1b-ing": "Progression : 1a-done, 1b-ing", "进度：1a-ing": "Progression : 1a-ing", "进度：1a1b-done": "Progression : 1a1b-done", "进度：1a1b-done, 1cing": "Progression : 1a1b-done, 1cing", "进度：all-done": "Progression : all-done", "需要合成的切分前文本": "Texte préalable à la synthèse", "需要合成的文本": "Texte à synthétiser", "需要合成的语种": "Langue de synthèse requise", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Si >= 3, utilisez le résultat de la reconnaissance de hauteur de récolte avec un filtre médian, la valeur est le rayon du filtre, son utilisation peut atténuer les sons sourds", "A模型权重": "Poids du modèle A", "A模型路径": "Chemin du modèle A", "B模型路径": "Chemin du modèle B", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "<PERSON><PERSON>er de courbe F0, option<PERSON>, une ligne par hauteur de ton, remplace F0 et la hauteur de ton par défaut", "Index Rate": "Taux d'index", "Onnx导出": "Exportation Onnx", "Onnx输出路径": "Chemin d'exportation Onnx", "RVC模型路径": "Chemin du modèle RVC", "ckpt处理": "Traitement des points de contrôle", "harvest进程数": "Nombre de processus de récolte", "index文件路径不可包含中文": "Le chemin du fichier d'index ne peut pas contenir de caractères chinois", "pth文件路径不可包含中文": "Le chemin du fichier pth ne peut pas contenir de caractères chinois", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Configuration des numéros de carte rmvpe : séparez les numéros de carte utilisés en entrée par des tirets, par exemple 0-0-1 signifie 2 processus sur la carte 0 et 1 processus sur la carte 1", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Étape 1 : Remplissez la configuration de l'expérience. Les données de l'expérience sont stockées dans le dossier logs, chaque expérience a son propre dossier. <PERSON><PERSON> devez entrer manuellement le chemin du nom de l'expérience, qui contient la configuration de l'expérience, les journaux et les fichiers de modèle entraînés.", "step1:正在处理数据": "Étape 1 : Traitement des données en cours", "step2:正在提取音高&正在提取特征": "Étape 2 : Extraction de la hauteur tonale et des caractéristiques en cours", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Étape 2a : Parcours automatique de tous les fichiers décodables en audio dans le dossier d'entraînement et normalisation par découpage. Deux dossiers wav sont générés dans le répertoire de l'expérience. Actuellement, seule la formation individuelle est prise en charge.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Étape 2b : Extraction de la hauteur tonale avec le CPU (si le modèle a une hauteur tonale) et extraction des caractéristiques avec le GPU (choisissez le numéro de la carte)", "step3: 填写训练设置, 开始训练模型和索引": "Étape 3 : <PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres d'entraînement et commencez l'entraînement du modèle et de l'index", "step3a:正在训练模型": "Étape 3a : Entraînement du modèle en cours", "一键训练": "Entraînement en un clic", "也可批量输入音频文件, 二选一, 优先读文件夹": "Également possible d'entrer en lot des fichiers audio, au choix, privilégiez la lecture du dossier", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Numéros de carte utilisés en entrée séparés par des tirets, par exemple   0-1-2   Utilisez les cartes 0, 1 et 2", "伴奏人声分离&去混响&去回声": "Séparation de la voix et de l'accompagnement, suppression de la réverbération et de l'écho", "使用模型采样率": "Taux d'échantillonnage du modèle", "使用设备采样率": "Taux d'échantillonnage de l'appareil", "保存名": "Nom de sauvegarde", "保存的文件名, 默认空为和源文件同名": "Nom de fichier sauvegardé, par défaut vide pour avoir le même nom que le fichier source", "保存的模型名不带后缀": "Nom du modèle sauvegardé sans suffixe", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Protéger les consonnes claires et les sons de respiration, éviter les artefacts tels que le déchirement du son électronique, tirer à 0.5 pour désactiver, diminuer pour augmenter la protection mais cela peut réduire l'efficacité de l'indexation", "修改": "Modifier", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modifier les informations du modèle (uniquement pour les petits fichiers de modèle extraits sous le dossier weights)", "停止音频转换": "Arrêter la conversion audio", "全流程结束！": "Processus complet terminé !", "刷新音色列表和索引路径": "Actualiser la liste des timbres et les chemins d'index", "加载模型": "Charger le modèle", "加载预训练底模D路径": "Charger le chemin du modèle de base pré-entraîné D", "加载预训练底模G路径": "Charger le chemin du modèle de base pré-entraîné G", "单次推理": "Inférence unique", "卸载音色省显存": "Décharger le timbre pour économiser la mémoire vidéo", "变调(整数, 半音数量, 升八度12降八度-12)": "Changer la tonalité (entier, quantité de demi-tons, monter d'une octave 12, descendre d'une octave -12)", "后处理重采样至最终采样率，0为不进行重采样": "Re-échantillonnage en post-traitement à la fréquence d'échantillonnage finale, 0 pour ne pas effectuer de re-échantillonnage", "否": "Non", "启用相位声码器": "Activer le codeur de phase", "响应阈值": "Seuil de réponse", "响度因子": "Facteur de volume sonore", "处理数据": "Trai<PERSON> les données", "导出Onnx模型": "Exporter le modèle Onnx", "导出文件格式": "Format d'exportation du fichier", "常见问题解答": "Questions fréquemment posées", "常规设置": "Paramètres généraux", "开始音频转换": "Démarrer la conversion audio", "性能设置": "Paramètres de performance", "批量推理": "Inférence en lot", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversion en lot, entrez le dossier audio à convertir, ou téléchargez plusieurs fichiers audio, les fichiers convertis seront enregistrés dans le dossier spécifié (opt par défaut).", "指定输出主人声文件夹": "Spécifier le dossier de sortie pour la voix principale", "指定输出文件夹": "Spécifier le dossier de sortie", "指定输出非主人声文件夹": "Spécifier le dossier de sortie pour la non-voix principale", "推理时间(ms):": "Temps d'inférence (ms) :", "推理音色": "Timbre d'inférence", "提取": "Extraire", "提取音高和处理数据使用的CPU进程数": "Nombre de processus CPU utilisés pour extraire la hauteur tonale et traiter les données", "是": "O<PERSON>", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Mettre en cache ou non tous les ensembles d'entraînement dans la mémoire vidéo. Pour les petites données de moins de 10 minutes, la mise en cache peut accélérer l'entraînement, mais pour les grandes données, la mise en cache peut épuiser la mémoire vidéo sans améliorer considérablement la vitesse.", "查看": "Voir", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Voir les informations du modèle (uniquement pour les petits fichiers de modèle extraits sous le dossier weights)", "检索特征占比": "Pourcentage des caractéristiques extraites", "模型": "<PERSON><PERSON><PERSON><PERSON>", "模型推理": "Inférence du modèle", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extraction du modèle (saisissez le chemin du modèle volumineux sous le dossier logs), utilisé lorsque l'entraînement est à mi-chemin, que vous ne voulez pas continuer l'entraînement, que le modèle n'a pas été automatiquement extrait et sauvegardé en tant que petit fichier, ou que vous souhaitez tester le modèle intermédiaire.", "模型是否带音高指导": "Le modèle inclut-il un guidage en hauteur tonale", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Le modèle inclut-il un guidage en hauteur tonale (nécessaire pour le chant, facultatif pour la parole)", "模型是否带音高指导,1是0否": "Le modèle inclut-il un guidage en hauteur tonale, 1 pour oui, 0 pour non", "模型版本型号": "Numéro de version du modèle", "模型融合, 可用于测试音色融合": "Fusion de modèles, utilisée pour tester la fusion des timbres", "模型路径": "<PERSON>em<PERSON> du modèle", "淡入淡出长度": "Longueur du fondu enchaîné", "版本": "Version", "特征提取": "Extraction des caractéristiques", "特征检索库文件路径,为空则使用下拉的选择结果": "Chemin du fichier de bibliothèque de recherche de caractéristiques, laisser vide pour utiliser le résultat de la liste déroulante", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Recommandation pour la transformation homme vers femme +12 clés, femme vers homme -12 clés, ajustez vous-même si l'étendue du son explose et provoque une distorsion de la voix.", "目标采样率": "Taux d'échantillonnage cible", "算法延迟(ms):": "Retard de l'algorithme (ms):", "自动检测index路径,下拉式选择(dropdown)": "Détection automatique du chemin de l'index, choix dans la liste déroulante", "融合": "Fusion", "要改的模型信息": "Informations du modèle à modifier", "要置入的模型信息": "Informations du modèle à insérer", "训练": "Entraînement", "训练模型": "Entraîner le modèle", "训练特征索引": "Entraîner l'index des caractéristiques", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Entraînement terminé, vous pouvez consulter les journaux d'entraînement de la console ou le fichier train.log dans le dossier d'expérience", "请指定说话人id": "Veuillez spécifier l'ID du locuteur", "请选择index文件": "Veuillez choisir le fichier d'index", "请选择pth文件": "Veuillez choisir le fichier pth", "请选择说话人id": "Veuillez choisir l'ID du locuteur", "转换": "Conversion", "输入实验名": "Nom de l'expérience d'entrée", "输入待处理音频文件夹路径": "Entrez le chemin du dossier audio à traiter", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Entrez le chemin du dossier audio à traiter (copiez-le depuis la barre d'adresse du gestionnaire de fichiers)", "输入待处理音频文件路径(默认是正确格式示例)": "Entrez le chemin du fichier audio à traiter (par défaut, c'est un exemple de format correct)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Entrez le taux de fusion pour remplacer l'enveloppe de volume source par l'enveloppe de volume de sortie, plus proche de 1, plus l'enveloppe de sortie est utilisée", "输入监听": "Entrée d'écoute", "输入训练文件夹路径": "Entrez le chemin du dossier d'entraînement", "输入设备": "Entrée de l'appareil", "输入降噪": "Entrée de réduction du bruit", "输出信息": "Sortie d'information", "输出变声": "Sortie de la transformation de la voix", "输出设备": "Sortie de l'appareil", "输出降噪": "Sortie de réduction du bruit", "输出音频(右下角三个点,点了可以下载)": "Sortie audio (trois points en bas à droite, cliquez pour télécharger)", "选择.index文件": "Choisissez le fichier .index", "选择.pth文件": "Choisissez le fichier .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "Choisissez l'algorithme d'extraction de hauteur tonale, vous pouvez utiliser pm pour accélérer l'entrée de la voix, harvest est bon pour les basses mais très lent, crepe a un bon effet mais utilise le GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Choisissez l'algorithme d'extraction de hauteur tonale, vous pouvez utiliser pm pour accélérer l'entrée de la voix, harvest est bon pour les basses mais très lent, crepe a un bon effet mais utilise le GPU, rmvpe a le meilleur effet et utilise légèrement le GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Choisissez l'algorithme d'extraction de hauteur tonale : utilisez pm pour accélérer l'entrée de la voix, une voix de haute qualité mais nécessite une meilleure CPU ; utilisez dio pour accélérer, harvest a une meilleure qualité mais est lent, rmvpe a le meilleur effet et utilise légèrement la CPU/GPU", "采样率:": "Taux d'échantillonnage:", "采样长度": "Longueur d'échantillonnage", "重载设备列表": "Recharger la liste des appareils", "音调设置": "Paramètres de tonalité", "音频设备(请使用同种类驱动)": "Appareil audio (veuillez utiliser un pilote de même type)", "音高算法": "Algorithme de hauteur tonale", "额外推理时长": "Durée d'inférence supplémentaire"}