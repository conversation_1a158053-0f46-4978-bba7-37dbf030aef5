{"很遗憾您这没有能用的显卡来支持您训练": "Unfortunately, there is no compatible GPU available to support your training.", "UVR5已开启": "UVR5 opened ", "UVR5已关闭": "UVR5 closed", "输入文件夹路径": "Input folder path", "输出文件夹路径": "Output folder path", "ASR 模型": "ASR model", "ASR 模型尺寸": "ASR model size", "ASR 语言设置": "ASR language", "模型切换": "Model switch", "是否开启dpo训练选项(实验性)": "Enable DPO training (experimental feature)", "开启无参考文本模式。不填参考文本亦相当于开启。": "Enable no reference mode. If you don't fill 'Text for reference audio', no reference mode will be enabled.", "使用无参考文本模式时建议使用微调的GPT": "Please use your trained GPT model if you don't use reference audio.", "后续将支持转音素、手工修改音素、语音合成分步执行。": " Step-to-step phoneme transformation and modification coming soon!", "gpt采样参数(无参考文本时不要太低)：": "GPT parameters:", "按标点符号切": "Slice by every punct", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "This software is open source under the MIT license. The author does not have any control over the software. Users who use the software and distribute the sounds exported by the software are solely responsible. <br>If you do not agree with this clause, you cannot use or reference any codes and files within the software package. See the root directory <b>Agreement-LICENSE</b> for details.", "0-前置数据集获取工具": "0-Fetch dataset", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5 webui (for vocal separation, deecho, dereverb and denoise)", "是否开启UVR5-WebUI": "Open UVR5-WebUI", "UVR5进程输出信息": "UVR5 process output log", "0b-语音切分工具": "0b-Audio slicer", ".list标注文件的路径": ".list annotation file path", "GPT模型列表": "GPT weight list", "SoVITS模型列表": "SoVITS weight list", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "Fill in the directory of segmented audio. The complete path of the read audio file is equal to the directory concatenated with the waveform's corresponding filename from the list file (not the full path).", "音频自动切分输入路径，可文件可文件夹": "Audio slicer input (file or folder)", "切分后的子音频的输出根目录": "Audio slicer output folder", "怎么切": "How to slice the sentence", "不切": "No slice", "凑四句一切": "Slice once every 4 sentences", "按英文句号.切": "Slice by English punct", "threshold:音量小于这个值视作静音的备选切割点": "Noise gate threshold (loudness below this value will be treated as noise", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "Minimum length", "min_interval:最短切割间隔": "Minumum interval for audio cutting", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: FO hop size, the smaller the value, the higher the accuracy）", "max_sil_kept:切完后静音最多留多长": "Maximum length for silence to be kept", "开启语音切割": "Start audio slicer", "终止语音切割": "Stop audio cutting", "max:归一化后最大值多少": "Loudness multiplier after normalized", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proportion of normalized audio merged into dataset", "切割使用的进程数": "CPU threads used for audio slicing", "语音切割进程输出信息": "Audio slicer output log", "0c-中文批量离线ASR工具": "0c-Chinese ASR tool", "开启离线批量ASR": "Start batch ASR", "终止ASR进程": "Stop ASR task", "批量ASR(中文only)输入文件夹路径": "Batch ASR (Chinese only) input folder", "ASR进程输出信息": "ASR output log", "0d-语音文本校对标注工具": "0d-Speech to text proofreading tool", "是否开启打标WebUI": "Open labelling WebUI", "打标数据标注文件路径": "path to proofreading text file", "打标工具进程输出信息": "Proofreading tool output log", "1-GPT-SoVITS-TTS": "1-GPT-SOVITS-TTS", "*实验/模型名": "*Experiment/model name", "显卡信息": "GPU Information", "预训练的SoVITS-G模型路径": "Pretrained SoVITS-G model path", "预训练的SoVITS-D模型路径": "Pretrained SoVITS-D model path", "预训练的GPT模型路径": "Pretrained GPT model path", "1A-训练集格式化工具": "1A-Dataset formatting", "输出logs/实验名目录下应有23456开头的文件和文件夹": "output folder (logs/{experiment name}) should have files and folders starts with 23456.", "*文本标注文件": "*Text labelling file", "*训练集音频文件目录": "*Audio dataset folder", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "Training the file name corresponding to the waveform of the waveform in the List file of the audio file", "1Aa-文本内容": "1Aa-Text", "GPU卡号以-分割，每个卡号一个进程": "GPU number is separated by -, each GPU will run one process ", "预训练的中文BERT模型路径": " Pretrained BERT model path", "开启文本获取": "Start speech-to-text", "终止文本获取进程": "Stop speech-to-text", "文本进程输出信息": "Text processing output", "1Ab-SSL自监督特征提取": "1Ab-SSL self-supervised feature extraction", "预训练的SSL模型路径": "Pretrained SSL model path", "开启SSL提取": "Start SSL extracting", "终止SSL提取进程": "Stop SSL extraction", "SSL进程输出信息": "SSL output log", "1Ac-语义token提取": "1Ac-semantics token extraction", "开启语义token提取": "Start semantics token extraction", "终止语义token提取进程": "Stop semantics token extraction", "语义token提取进程输出信息": "Sematics token extraction output log", "1Aabc-训练集格式化一键三连": "1Aabc-One-click formatting", "开启一键三连": "Start one-click formatting", "终止一键三连": "Stop one-click formatting", "一键三连进程输出信息": "One-click formatting output", "1B-微调训练": "1B-Fine-tuned training", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS training. The model is located in SoVITS_weights.", "每张显卡的batch_size": "Batch size per GPU:", "总训练轮数total_epoch，不建议太高": "Total epochs, do not increase to a value that is too high", "文本模块学习率权重": "Text model learning rate weighting", "保存频率save_every_epoch": "Save frequency (save_every_epoch):", "是否仅保存最新的ckpt文件以节省硬盘空间": "Save only the latest '.ckpt' file to save disk space:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Save a small final model to the 'weights' folder at each save point:", "开启SoVITS训练": "Start SoVITS training", "终止SoVITS训练": "Stop SoVITS training", "SoVITS训练进程输出信息": "SoVITS training output log", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT training. The model is located in GPT_weights.", "总训练轮数total_epoch": "Total training epochs (total_epoch):", "开启GPT训练": "Start GPT training", "终止GPT训练": "Stop GPT training", "GPT训练进程输出信息": "GPT training output log", "1C-推理": "1C-inference", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Choose the models from SoVITS_weights and GPT_weights. The default one is a pretrain, so you can experience zero shot TTS.", "*GPT模型列表": "*GPT models list", "*SoVITS模型列表": "*SoVITS models list", "GPU卡号,只能填1个整数": "GPU number, can only input ONE integer", "刷新模型路径": "refreshing model paths", "是否开启TTS推理WebUI": "Open TTS inference WEBUI", "TTS推理WebUI进程输出信息": "TTS inference webui output log", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Voice Changer", "施工中，请静候佳音": "In construction, please wait", "参考音频在3~10秒范围外，请更换！": "Reference audio is outside the 3-10 second range, please choose another one!", "请上传3~10秒内参考音频，超过会报错！": "Please upload a reference audio within the 3-10 second range; if it exceeds this duration, it will raise errors.", "TTS推理进程已开启": "TTS inference process is opened", "TTS推理进程已关闭": "TTS inference process closed", "打标工具WebUI已开启": "proofreading tool webui is opened", "打标工具WebUI已关闭": "proofreading tool webui is closed", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "This software is under MIT licence. The author does not have any control for this software. Users are solely reponsible for all voices thats being converted and/or distributed. If you disagree with this Terms and Conditions, you cannot use or cite any files or code in this file. Please check LICENSE. for more info.", "*请上传并填写参考信息": "*Please upload and fill reference information", "*请填写需要合成的目标文本。中英混合选中文，日英混合选日文，中日混合暂不支持，非目标语言文本自动遗弃。": "*Please fill the text that needs inference. Select Chinese for mixed Chinese and English text, choose Japanese for mixed Japanese and English text. Mixed Chinese and Japanese is currently not supported; non-target language text will be automatically discarded.", "ASR任务开启：%s": "ASR training started: %s", "GPT训练完成": "Finished GPT training", "GPT训练开始：%s": "GPT training started: %s", "SSL提取进程执行中": "SSL extracting", "SSL提取进程结束": "SSL extraction finished", "SoVITS训练完成": "SoVITS training finished", "SoVITS训练开始：%s": "SoVITS training started：%s", "一键三连中途报错": "An error has occured during One-click formatting", "一键三连进程结束": "Finished one-click formatting", "中文": "Chinese", "凑50字一切": "Cut per 50 characters", "凑五句一切": "Cut per 5 sentences", "切分后文本": "Text after sliced", "切割执行中": "Slicing audio", "切割结束": "finished audio slicing", "参考音频的文本": "Text for reference audio", "参考音频的语种": "Language for reference audio", "合成语音": "Start inference", "后续将支持混合语种编码文本输入。": "Mixed languages input will be supported soon.", "已有正在进行的ASR任务，需先终止才能开启下一次任务": " An ASR task is already in progress, please stop before starting the next task", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "A GPT training task is already in progress, please stop before starting the next task", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "A SSL extraction task is already in progress, please stop before starting the next task", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "A SoVITS training task is already in progress, please stop before starting the next task", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "An ASR task is already in progress, please stop before starting the next task", "已有正在进行的切割任务，需先终止才能开启下一次任务": "An audio slicing task is already in progress, please stop before starting the next task", "已有正在进行的文本任务，需先终止才能开启下一次任务": "A TTS proofreading task is already in progress, please stop before starting the next task", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "A semantics token extraction task is already in progress, please stop before starting the next task", "已终止ASR进程": "ASR task has been stopped", "已终止GPT训练": "GPT training has been stopped", "已终止SoVITS训练": "SoVITS training has been stopped", "已终止所有1a进程": "All 1a tasks has been stopped", "已终止所有1b进程": "All 1b tasks has been stopped", "已终止所有一键三连进程": "All one-clicking formatting tasks has been stopped", "已终止所有切割进程": "All audio slicing tasks has been stopped", "已终止所有语义token进程": "All semantics token tasks has been stopped", "按中文句号。切": "Slice by Chinese punct", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Text slicer tool, since there will be issues when infering long texts, so it is advised to cut first. When infering, it will infer respectively then combined together.", "文本进程执行中": "Text processing", "文本进程结束": "Finished text processing", "日文": "Japanese", "英文": "English", "语义token提取进程执行中": "Semantics token extracting", "语义token提取进程结束": "Finished semantics token extraction", "请上传参考音频": "Please upload reference audio", "输入路径不存在": "No input file or directory", "输入路径存在但既不是文件也不是文件夹": "Input directory exists, but it is not a file or a folder", "输出的语音": "Inference Result", "进度：1a-done": "Progress：1a-done", "进度：1a-done, 1b-ing": "Progress：1a-done, 1b-ing", "进度：1a-ing": "Progress：1a-ing", "进度：1a1b-done": "Progress：1a1b-done", "进度：1a1b-done, 1cing": "Progress：1a1b-done, 1cing", "进度：all-done": "Progress：all-done", "需要合成的切分前文本": "Inference text that needs to be sliced", "需要合成的文本": "Inference text", "需要合成的语种": "Inference text language", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "If >=3: apply median filtering to the harvested pitch results. The value represents the filter radius and can reduce breathiness.", "A模型权重": "Weight (w) for Model A:", "A模型路径": "Path to Model A:", "B模型路径": "Path to Model B:", "E:\\语音音频+标注\\米津玄师\\src": "C:\\Users\\<USER>\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0 curve file (optional). One pitch per line. Replaces the default F0 and pitch modulation:", "Index Rate": "Index Rate", "Onnx导出": "Export Onnx", "Onnx输出路径": "Onnx Export Path:", "RVC模型路径": "RVC Model Path:", "ckpt处理": "ckpt Processing", "harvest进程数": "Number of CPU processes used for harvest pitch algorithm", "index文件路径不可包含中文": "index文件路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Enter the GPU index(es) separated by '-', e.g., 0-0-1 to use 2 processes in GPU0 and 1 process in GPU1", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Step 1: Fill in the experimental configuration. Experimental data is stored in the 'logs' folder, with each experiment having a separate folder. Manually enter the experiment name path, which contains the experimental configuration, logs, and trained model files.", "step1:正在处理数据": "Step 1: Processing data", "step2:正在提取音高&正在提取特征": "step2:Pitch extraction & feature extraction", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Step 2a: Automatically traverse all files in the training folder that can be decoded into audio and perform slice normalization. Generates 2 wav folders in the experiment directory. Currently, only single-singer/speaker training is supported.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Step 2b: Use CPU to extract pitch (if the model has pitch), use GPU to extract features (select GPU index):", "step3: 填写训练设置, 开始训练模型和索引": "Step 3: Fill in the training settings and start training the model and index", "step3a:正在训练模型": "Step 3a: Model training started", "一键训练": "One-click training", "也可批量输入音频文件, 二选一, 优先读文件夹": "Multiple audio files can also be imported. If a folder path exists, this input is ignored.", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Batch processing for vocal accompaniment separation using the UVR5 model.<br>Example of a valid folder path format: D:\\path\\to\\input\\folder (copy it from the file manager address bar).<br>The model is divided into three categories:<br>1. Preserve vocals: Choose this option for audio without harmonies. It preserves vocals better than HP5. It includes two built-in models: HP2 and HP3. HP3 may slightly leak accompaniment but preserves vocals slightly better than HP2.<br>2. Preserve main vocals only: Choose this option for audio with harmonies. It may weaken the main vocals. It includes one built-in model: HP5.<br>3. De-reverb and de-delay models (by FoxJoy):<br>  (1) MDX-Net: The best choice for stereo reverb removal but cannot remove mono reverb;<br>&emsp;(234) DeEcho: Removes delay effects. Aggressive mode removes more thoroughly than Normal mode. DeReverb additionally removes reverb and can remove mono reverb, but not very effectively for heavily reverberated high-frequency content.<br>De-reverb/de-delay notes:<br>1. The processing time for the DeEcho-DeReverb model is approximately twice as long as the other two DeEcho models.<br>2. The MDX-Net-Dereverb model is quite slow.<br>3. The recommended cleanest configuration is to apply MDX-Net first and then DeEcho-Aggressive.", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Enter the GPU index(es) separated by '-', e.g., 0-1-2 to use GPU 0, 1, and 2:", "伴奏人声分离&去混响&去回声": "Vocals/Accompaniment Separation & Reverberation Removal", "使用模型采样率": "使用模型采样率", "使用设备采样率": "使用设备采样率", "保存名": "Save name:", "保存的文件名, 默认空为和源文件同名": "Save file name (default: same as the source file):", "保存的模型名不带后缀": "Saved model name (without extension):", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Protect voiceless consonants and breath sounds to prevent artifacts such as tearing in electronic music. Set to 0.5 to disable. Decrease the value to increase protection, but it may reduce indexing accuracy:", "修改": "Modify", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modify model information (only supported for small model files extracted from the 'weights' folder)", "停止音频转换": "Stop audio conversion", "全流程结束！": "All processes have been completed!", "刷新音色列表和索引路径": "Refresh voice list and index path", "加载模型": "Load model", "加载预训练底模D路径": "Load pre-trained base model D path:", "加载预训练底模G路径": "Load pre-trained base model G path:", "单次推理": "Single Inference", "卸载音色省显存": "Unload voice to save GPU memory:", "变调(整数, 半音数量, 升八度12降八度-12)": "Transpose (integer, number of semitones, raise by an octave: 12, lower by an octave: -12):", "后处理重采样至最终采样率，0为不进行重采样": "Resample the output audio in post-processing to the final sample rate. Set to 0 for no resampling:", "否": "No", "启用相位声码器": "启用相位声码器", "响应阈值": "Response threshold", "响度因子": "loudness factor", "处理数据": "Process data", "导出Onnx模型": "Export Onnx Model", "导出文件格式": "Export file format", "常见问题解答": "FAQ (Frequently Asked Questions)", "常规设置": "General settings", "开始音频转换": "Start audio conversion", "性能设置": "Performance settings", "批量推理": "Batch Inference", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Batch conversion. Enter the folder containing the audio files to be converted or upload multiple audio files. The converted audio will be output in the specified folder (default: 'opt').", "指定输出主人声文件夹": "Specify the output folder for vocals:", "指定输出文件夹": "Specify output folder:", "指定输出非主人声文件夹": "Specify the output folder for accompaniment:", "推理时间(ms):": "Inference time (ms):", "推理音色": "Inferencing voice:", "提取": "Extract", "提取音高和处理数据使用的CPU进程数": "Number of CPU processes used for pitch extraction and data processing:", "是": "Yes", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Cache all training sets to GPU memory. Caching small datasets (less than 10 minutes) can speed up training, but caching large datasets will consume a lot of GPU memory and may not provide much speed improvement:", "查看": "View", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "View model information (only supported for small model files extracted from the 'weights' folder)", "检索特征占比": "Search feature ratio (controls accent strength, too high has artifacting):", "模型": "Model", "模型推理": "Model Inference", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Model extraction (enter the path of the large file model under the 'logs' folder). This is useful if you want to stop training halfway and manually extract and save a small model file, or if you want to test an intermediate model:", "模型是否带音高指导": "Whether the model has pitch guidance:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Whether the model has pitch guidance (required for singing, optional for speech):", "模型是否带音高指导,1是0否": "Whether the model has pitch guidance (1: yes, 0: no):", "模型版本型号": "Model architecture version:", "模型融合, 可用于测试音色融合": "Model fusion, can be used to test timbre fusion", "模型路径": "Path to Model:", "淡入淡出长度": "Fade length", "版本": "Version", "特征提取": "Feature extraction", "特征检索库文件路径,为空则使用下拉的选择结果": "Path to the feature index file. Leave blank to use the selected result from the dropdown:", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Recommended +12 key for male to female conversion, and -12 key for female to male conversion. If the sound range goes too far and the voice is distorted, you can also adjust it to the appropriate range by yourself.", "目标采样率": "Target sample rate:", "算法延迟(ms):": "Algorithmic delays(ms):", "自动检测index路径,下拉式选择(dropdown)": "Auto-detect index path and select from the dropdown:", "融合": "Fusion", "要改的模型信息": "Model information to be modified:", "要置入的模型信息": "Model information to be placed:", "训练": "Train", "训练模型": "Train model", "训练特征索引": "Train feature index", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Training complete. You can check the training logs in the console or the 'train.log' file under the experiment folder.", "请指定说话人id": "Please specify the speaker/singer ID:", "请选择index文件": "Please choose the .index file", "请选择pth文件": "Please choose the .pth file", "请选择说话人id": "Select Speaker/Singer ID:", "转换": "Convert", "输入实验名": "Enter the experiment name:", "输入待处理音频文件夹路径": "Enter the path of the audio folder to be processed:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Enter the path of the audio folder to be processed (copy it from the address bar of the file manager):", "输入待处理音频文件路径(默认是正确格式示例)": "Enter the path of the audio file to be processed (default is the correct format example):", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Adjust the volume envelope scaling. Closer to 0, the more it mimicks the volume of the original vocals. Can help mask noise and make volume sound more natural when set relatively low. Closer to 1 will be more of a consistently loud volume:", "输入监听": "Input voice monitor", "输入训练文件夹路径": "Enter the path of the training folder:", "输入设备": "Input device", "输入降噪": "Input noise reduction", "输出信息": "Output information", "输出变声": "Output converted voice", "输出设备": "Output device", "输出降噪": "Output noise reduction", "输出音频(右下角三个点,点了可以下载)": "Export audio (click on the three dots in the lower right corner to download)", "选择.index文件": "Select the .index file", "选择.pth文件": "Select the .pth file", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Select the pitch extraction algorithm ('pm': faster extraction but lower-quality speech; 'harvest': better bass but extremely slow; 'crepe': better quality but GPU intensive), 'rmvpe': best quality, and little GPU requirement", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Select the pitch extraction algorithm: when extracting singing, you can use 'pm' to speed up. For high-quality speech with fast performance, but worse CPU usage, you can use 'dio'. 'harvest' results in better quality but is slower.  'rmvpe' has the best results and consumes less CPU/GPU", "采样率:": "采样率:", "采样长度": "Sample length", "重载设备列表": "Reload device list", "音调设置": "Pitch settings", "音频设备(请使用同种类驱动)": "Audio device (please use the same type of driver)", "音高算法": "pitch detection algorithm", "额外推理时长": "Extra inference time"}