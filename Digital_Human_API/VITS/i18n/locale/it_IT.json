{"很遗憾您这没有能用的显卡来支持您训练": "Purtroppo non hai una scheda grafica utilizzabile per supportare il tuo addestramento", "UVR5已开启": "UVR5 è attivato", "UVR5已关闭": "UVR5 è disattivato", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Questo software è open source con licenza MIT. L'autore non ha alcun controllo sul software. L'utente che utilizza il software o diffonde i suoni derivati dal software ne è responsabile. <br>Se non accetti questi termini, non puoi utilizzare o citare alcun codice o file all'interno del pacchetto software. Vedi la cartella principale<b>LICENSE</b> per i dettagli.", "0-前置数据集获取工具": "0-Strumento di acquisizione del dataset preliminare", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Strumento di separazione voce e accompagnamento UVR5 & Rimozione riverbero e ritardo", "是否开启UVR5-WebUI": "Attivare UVR5-WebUI", "UVR5进程输出信息": "Informazioni sull'output del processo UVR5", "0b-语音切分工具": "0b-Strumento di segmentazione vocale", "音频自动切分输入路径，可文件可文件夹": "Percorso di input per la segmentazione automatica dell'audio, può essere un file o una cartella", "切分后的子音频的输出根目录": "Directory radice di output per gli audio segmentati", "threshold:音量小于这个值视作静音的备选切割点": "threshold: Punto di taglio alternativo considerato silenzioso se il volume è inferiore a questo valore", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: <PERSON>ng<PERSON>zza minima di ogni segmento. Se il primo segmento è troppo corto, verrà unito agli segmenti successivi fino a superare questo valore", "min_interval:最短切割间隔": "min_interval: Intervallo minimo di taglio", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: Come calcolare la curva del volume. <PERSON><PERSON> piccolo <PERSON>, maggiore è la precisione ma aumenta la complessità computazionale (non significa che una maggiore precisione dà risultati migliori)", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: Massima durata del silenzio dopo il taglio", "开启语音切割": "Attivare la segmentazione vocale", "终止语音切割": "Terminare la segmentazione vocale", "max:归一化后最大值多少": "max: <PERSON><PERSON> valore dopo la normalizzazione", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Quanta proporzione dell'audio normalizzato deve essere miscelata", "切割使用的进程数": "Numero di processi utilizzati per il taglio", "语音切割进程输出信息": "Informazioni sull'output del processo di segmentazione vocale", "0c-中文批量离线ASR工具": "0c-Strumento di ASR offline batch in cinese", "开启离线批量ASR": "Attivare ASR offline batch", "终止ASR进程": "Terminare il processo ASR", "批量ASR(中文only)输入文件夹路径": "Percorso della cartella di input per ASR offline batch (solo cinese)", "ASR进程输出信息": "Informazioni sull'output del processo ASR", "0d-语音文本校对标注工具": "0d-Strumento di correzione e annotazione testo vocale", "是否开启打标WebUI": "Attivare l'interfaccia utente Web di annotazione", "打标数据标注文件路径": "Percorso del file di annotazione dei dati contrassegnati", "打标工具进程输出信息": "Informazioni sull'output del processo di annotazione", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*Nome dell'esperimento/modello", "显卡信息": "Informazioni sulla scheda grafica", "预训练的SoVITS-G模型路径": "Percorso del modello preaddestrato SoVITS-G", "预训练的SoVITS-D模型路径": "Percorso del modello preaddestrato SoVITS-D", "预训练的GPT模型路径": "Percorso del modello preaddestrato GPT", "1A-训练集格式化工具": "1A-Strumento di formattazione del set di addestramento", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Nella cartella logs/nome dell'esperimento dovrebbero esserci file e cartelle che iniziano con 23456", "*文本标注文件": "*File di annotazione del testo", "*训练集音频文件目录": "*Directory dei file audio del set di addestramento", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "Directory dei file audio del set di addestramento, concatenare il nome del file corrispondente nella lista", "1Aa-文本内容": "1Aa-Contenuto del testo", "GPU卡号以-分割，每个卡号一个进程": "Numero di GPU separati da '-'; ogni numero corrisponde a un processo", "预训练的中文BERT模型路径": "Percorso del modello BERT cinese preaddestrato", "开启文本获取": "Attivare l'estrazione del testo", "终止文本获取进程": "Terminare il processo di estrazione del testo", "文本进程输出信息": "Informazioni sull'output del processo di estrazione del testo", "1Ab-SSL自监督特征提取": "1Ab-Estrazione di caratteristiche auto-supervisionata SSL", "预训练的SSL模型路径": "Percorso del modello SSL preaddestrato", "开启SSL提取": "Attivare l'estrazione SSL", "终止SSL提取进程": "Terminare il processo di estrazione SSL", "SSL进程输出信息": "Informazioni sull'output del processo SSL", "1Ac-语义token提取": "1Ac-Estrazione del token semantico", "开启语义token提取": "Attivare l'estrazione del token semantico", "终止语义token提取进程": "Terminare il processo di estrazione del token semantico", "语义token提取进程输出信息": "Informazioni sull'output del processo di estrazione del token semantico", "1Aabc-训练集格式化一键三连": "1Aabc-Strumento di formattazione del set di addestramento con tre passaggi", "开启一键三连": "Attivare la formattazione con tre passaggi", "终止一键三连": "Terminare la formattazione con tre passaggi", "一键三连进程输出信息": "Informazioni sull'output del processo di 'One Click Three Connect'", "1B-微调训练": "1B-Allenamento di affinamento", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Allenamento di SoVITS. I file del modello destinati alla condivisione sono salvati in SoVITS_weights.", "每张显卡的batch_size": "Batch size per ogni scheda grafica", "总训练轮数total_epoch，不建议太高": "Numero totale di epoche di addestramento, non raccomandato troppo alto", "文本模块学习率权重": "Peso del tasso di apprendimento del modulo di testo", "保存频率save_every_epoch": "Frequenza di salvataggio ogni epoca", "是否仅保存最新的ckpt文件以节省硬盘空间": "<PERSON><PERSON><PERSON> solo il file ckpt più recente per risparmiare spazio su disco", "是否在每次保存时间点将最终小模型保存至weights文件夹": "<PERSON><PERSON><PERSON> il modello finale più piccolo nella cartella weights ad ogni punto di salvataggio", "开启SoVITS训练": "Attivare l'allenamento di SoVITS", "终止SoVITS训练": "Terminare l'allenamento di SoVITS", "SoVITS训练进程输出信息": "Informazioni sull'output del processo di allenamento di SoVITS", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Allenamento di GPT. I file del modello destinati alla condivisione sono salvati in GPT_weights.", "总训练轮数total_epoch": "Numero totale di epoche di addestramento", "开启GPT训练": "Attivare l'allenamento di GPT", "终止GPT训练": "Terminare l'allenamento di GPT", "GPT训练进程输出信息": "Informazioni sull'output del processo di allenamento di GPT", "1C-推理": "1C-Inferenza", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "<PERSON><PERSON><PERSON> il modello salvato in SoVITS_weights e GPT_weights dopo l'addestramento. Uno di default è il modello di base, utilizzato per l'esperienza di Zero Shot TTS in 5 secondi.", "*GPT模型列表": "*Lista dei modelli GPT", "*SoVITS模型列表": "*Lista dei modelli SoVITS", "GPU卡号,只能填1个整数": "Numero della scheda grafica, può essere inserito solo un numero intero", "刷新模型路径": "Aggiorna il percorso del modello", "是否开启TTS推理WebUI": "Attivare l'interfaccia utente Web per l'inferenza TTS", "TTS推理WebUI进程输出信息": "Informazioni sull'output del processo dell'interfaccia utente Web per l'inferenza TTS", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Voce modificata", "施工中，请静候佳音": "In costruzione, attendi pazientemente le buone notizie", "TTS推理进程已开启": "Il processo di inferenza TTS è stato avviato", "TTS推理进程已关闭": "Il processo di inferenza TTS è stato chiuso", "打标工具WebUI已开启": "L'interfaccia utente Web dello strumento di annotazione è stata avviata", "打标工具WebUI已关闭": "L'interfaccia utente Web dello strumento di annotazione è stata chiusa", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "Questo software è open source con licenza MIT. L'autore non ha alcun controllo sul software. L'utente che utilizza il software o diffonde i suoni derivati dal software ne è responsabile. Se non accetti questi termini, non puoi utilizzare o citare alcun codice o file all'interno del pacchetto software. Vedi la cartella principale LICENSE per i dettagli.", "*请上传并填写参考信息": "*Carica e compila le informazioni di riferimento", "*请填写需要合成的目标文本": "*Compila il testo di destinazione da sintetizzare", "ASR任务开启：%s": "Attività ASR avviata: %s", "GPT训练完成": "Allenamento di GPT completato", "GPT训练开始：%s": "Inizio dell'allenamento di GPT: %s", "SSL提取进程执行中": "Processo di estrazione SSL in corso", "SSL提取进程结束": "Processo di estrazione SSL completato", "SoVITS训练完成": "Allenamento di SoVITS completato", "SoVITS训练开始：%s": "Inizio dell'allenamento di SoVITS: %s", "一键三连中途报错": "Errore durante 'One Click Three Connect'", "一键三连进程结束": "Processo di 'One Click Three Connect' completato", "中文": "Cinese", "凑50字一切": "Riempire con 50 caratteri per tutto", "凑五句一切": "Riempire con cinque frasi per tutto", "切分后文本": "Testo dopo il taglio", "切割执行中": "<PERSON><PERSON> in corso", "切割结束": "Taglio completato", "参考音频的文本": "Testo dell'audio di riferimento", "参考音频的语种": "Lingua dell'audio di riferimento", "合成语音": "Sintesi vocale", "后续将支持混合语种编码文本输入。": "In futuro sarà supportata l'input di testi con codifica mista di lingue.", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "È già in corso un'attività ASR. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "<PERSON> già in corso un'attività di allenamento di GPT. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "È già in corso un'attività di estrazione SSL. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "È già in corso un'attività di allenamento di SoVITS. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "È già in corso un'attività di 'One Click Three Connect'. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的切割任务，需先终止才能开启下一次任务": "È già in corso un'attività di taglio. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的文本任务，需先终止才能开启下一次任务": "È già in corso un'attività di testo. Devi interromperla prima di avviare una nuova attività.", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "È già in corso un'attività di estrazione di token semantici. Devi interromperla prima di avviare una nuova attività.", "已终止ASR进程": "Il processo ASR è stato terminato", "已终止GPT训练": "L'allenamento di GPT è stato terminato", "已终止SoVITS训练": "Allenamento SoVITS terminato", "已终止所有1a进程": "Processi 1a terminati", "已终止所有1b进程": "Processi 1b terminati", "已终止所有一键三连进程": "Processi One Click Three Connect terminati", "已终止所有切割进程": "Processi di taglio terminati", "已终止所有语义token进程": "Processi di estrazione token semantici terminati", "按中文句号。切": "Taglia secondo il punto cinese.", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "Strumento di divisione del testo. I testi troppo lunghi potrebbero non avere un buon effetto di sintesi, quindi è consigliabile dividerli prima della sintesi. La sintesi verrà separata in base ai ritorni a capo nel testo e successivamente ricomposta.", "文本进程执行中": "Processo di testo in esecuzione", "文本进程结束": "Processo di testo terminato", "日文": "Giapponese", "英文": "<PERSON><PERSON><PERSON>", "语义token提取进程执行中": "Processo di estrazione token semantici in esecuzione", "语义token提取进程结束": "Processo di estrazione token semantici terminato", "请上传参考音频": "Carica l'audio di riferimento", "输入路径不存在": "Il percorso di input non esiste", "输入路径存在但既不是文件也不是文件夹": "Il percorso di input esiste ma non è né un file né una cartella", "输出的语音": "Audio di output", "进度：1a-done": "Progresso: 1a-done", "进度：1a-done, 1b-ing": "Progresso: 1a-done, 1b-ing", "进度：1a-ing": "Progresso: 1a-ing", "进度：1a1b-done": "Progresso: 1a1b-done", "进度：1a1b-done, 1cing": "Progresso: 1a1b-done, 1cing", "进度：all-done": "Progresso: all-done", "需要合成的切分前文本": "Testo da sintetizzare prima del taglio", "需要合成的文本": "<PERSON>o da sintetizzare", "需要合成的语种": "Lingua da sintetizzare", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Se >=3, usa il filtraggio mediano sui risultati del riconoscimento dell'altezza di harvest, il valore è il raggio del filtro. L'uso di questo valore può attenuare i suoni muti.", "A模型权重": "Peso del modello A", "A模型路径": "Percorso del modello A", "B模型路径": "Percorso del modello B", "E:\\语音音频+标注\\米津玄师\\src": "E:\\语音音频+标注\\米津玄师\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "File della curva F0, opzionale, una riga per un'altezza, sostituisce il F0 predefinito e le variazioni di tono", "Index Rate": "Tasso di indice", "Onnx导出": "Esporta in Onnx", "Onnx输出路径": "Percorso di output Onnx", "RVC模型路径": "Percorso del modello RVC", "ckpt处理": "Elaborazione del ckpt", "harvest进程数": "Numero di processi harvest", "index文件路径不可包含中文": "Il percorso del file di indice non può contenere caratteri cinesi", "pth文件路径不可包含中文": "Il percorso del file pth non può contenere caratteri cinesi", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "Configurazione dei numeri delle schede rmvpe: separa con - i numeri delle schede dei diversi processi utilizzati in input. Ad esempio, 0-0-1 utilizza 2 processi sulla scheda 0 e 1 processo sulla scheda 1", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Passo 1: Compila la configurazione sperimentale. I dati sperimentali sono salvati in logs, ogni esperimento in una cartella. È necessario inserire manualmente il percorso del nome dell'esperimento, contenente configurazione sperimentale, log e file di modello addestrato.", "step1:正在处理数据": "Passo 1: Elaborazione dei dati in corso", "step2:正在提取音高&正在提取特征": "Passo 2: Estrazione dell'altezza e delle caratteristiche in corso", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Passo 2a: Attraversa automaticamente tutti i file nella cartella di addestramento che possono essere decodificati in audio e li normalizza a fette. Nella cartella sperimentale vengono generate due cartelle wav; Al momento supporta solo l'addestramento singolo.", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Passo 2b: Usa la CPU per estrarre l'altezza (se il modello la include) e la GPU per estrarre le caratteristiche (scegliendo il numero della scheda)", "step3: 填写训练设置, 开始训练模型和索引": "Passo 3: Compila le impostazioni di addestramento, inizia ad addestrare il modello e l'indice", "step3a:正在训练模型": "Passo 3a: Addestramento del modello in corso", "一键训练": "Allenamento One-Click", "也可批量输入音频文件, 二选一, 优先读文件夹": "È possibile anche inserire file audio in batch, una delle due opzioni, con priorità alla lettura della cartella", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Numeri delle schede separati da - utilizzati in input, ad esempio 0-1-2, u<PERSON><PERSON><PERSON><PERSON> le schede 0, 1 e 2", "伴奏人声分离&去混响&去回声": "Separazione tra accompagnamento e voce & Rimozione dell'eco & Rimozione dell'eco", "使用模型采样率": "Frequenza di campionamento del modello", "使用设备采样率": "Frequenza di campionamento del dispositivo", "保存名": "Nome del salvataggio", "保存的文件名, 默认空为和源文件同名": "Nome del file salvato, vuoto di default è lo stesso del file sorgente", "保存的模型名不带后缀": "Nome del modello salvato senza estensione", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Protegge le consonanti chiare e i suoni di respirazione, evita artifact come la rottura del suono elettronico, tirare a 0.5 per disattivare, abbassare per aumentare la protezione ma potrebbe ridurre l'effetto di indicizzazione", "修改": "Modifica", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modifica le informazioni del modello (supporta solo i piccoli file di modello estratti dalla cartella weights)", "停止音频转换": "Interrompi la conversione audio", "全流程结束！": "Processo completo!", "刷新音色列表和索引路径": "Aggiorna la lista dei toni e il percorso dell'indice", "加载模型": "Carica il modello", "加载预训练底模D路径": "Carica il percorso del modello di fondo preaddestrato D", "加载预训练底模G路径": "Carica il percorso del modello di fondo preaddestrato G", "单次推理": "<PERSON>fer<PERSON><PERSON> singola", "卸载音色省显存": "Scarica il tono per risparmiare memoria video", "变调(整数, 半音数量, 升八度12降八度-12)": "Modifica del tono (numero intero, quantità di semitoni, 12 per un'ottava in su, -12 per un'ottava in giù)", "后处理重采样至最终采样率，0为不进行重采样": "Ricampiona in modo post-elaborazione alla frequenza di campionamento finale, 0 per non eseguire il ricampionamento", "否": "No", "启用相位声码器": "Abilita il codificatore di fase", "响应阈值": "Soglia di risposta", "响度因子": "Fattore di risposta", "处理数据": "Elaborazione dati", "导出Onnx模型": "Esporta il modello Onnx", "导出文件格式": "Formato di esportazione del file", "常见问题解答": "<PERSON><PERSON><PERSON>", "常规设置": "Impostazioni generali", "开始音频转换": "Inizia la conversione audio", "性能设置": "Impostazioni di performance", "批量推理": "Inferenza batch", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversione in batch, inserisci la cartella con i file audio da convertire o carica più file audio, i file convertiti verranno salvati nella cartella specificata (per impostazione predefinita opt).", "指定输出主人声文件夹": "Specifica la cartella di output per la voce principale", "指定输出文件夹": "Specifica la cartella di output", "指定输出非主人声文件夹": "Specifica la cartella di output per la non voce principale", "推理时间(ms):": "Tempo di inferenza (ms):", "推理音色": "Tono di inferenza", "提取": "Estrai", "提取音高和处理数据使用的CPU进程数": "Numero di processi CPU utilizzati per l'estrazione dell'altezza del suono e l'elaborazione dei dati", "是": "Sì", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Se memorizzare nella cache tutto l'insieme di addestramento nella memoria video. <PERSON><PERSON><PERSON> set di dati inferiori a 10 minuti possono essere memorizzati nella cache per accelerare l'addestramento, la memorizzazione nella cache di grandi set di dati può esaurire la memoria video e non accelerare di molto", "查看": "Visualizza", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Visualizza le informazioni del modello (supporta solo i piccoli file di modello estratti dalla cartella weights)", "检索特征占比": "Percentuale di caratteristiche di ricerca", "模型": "<PERSON><PERSON>", "模型推理": "Inferenza del modello", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Estrazione del modello (inserisci il percorso del modello di grandi dimensioni nella cartella logs), adatto per i modelli a metà addestramento che non si desidera continuare ad addestrare, i modelli non estratti automaticamente vengono salvati come modelli di piccole dimensioni o per testare la situazione del modello intermedio", "模型是否带音高指导": "Il modello include o meno la guida all'altezza del suono", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Il modello include o meno la guida all'altezza del suono (necessario per il canto, opzionale per la voce)", "模型是否带音高指导,1是0否": "Il modello include o meno la guida all'altezza del suono, 1 sì, 0 no", "模型版本型号": "Versione e modello del modello", "模型融合, 可用于测试音色融合": "Fusione dei modelli, utile per testare la fusione dei toni", "模型路径": "Percorso del modello", "淡入淡出长度": "<PERSON><PERSON><PERSON><PERSON> del fading in/fading out", "版本": "Versione", "特征提取": "Estrazione delle caratteristiche", "特征检索库文件路径,为空则使用下拉的选择结果": "Percorso del file della libreria di ricerca delle caratteristiche, se vuoto usa la selezione a discesa", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Consigliato +12 toni per la trasformazione da uomo a donna, -12 toni per la trasformazione da donna a uomo. Se l'intervallo tonale esplode causando distorsioni nel timbro, è possibile regolarlo manualmente nell'intervallo adatto.", "目标采样率": "Frequenza di campionamento obiettivo", "算法延迟(ms):": "<PERSON><PERSON> dell'algoritmo (ms):", "自动检测index路径,下拉式选择(dropdown)": "Rilevamento automatico del percorso dell'indice, selezione a discesa (dropdown)", "融合": "Fusione", "要改的模型信息": "Informazioni del modello da modificare", "要置入的模型信息": "Informazioni del modello da inserire", "训练": "Addestramento", "训练模型": "Addestra il modello", "训练特征索引": "Addestramento dell'indice delle caratteristiche", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Fine dell'addestramento, puoi visualizzare il registro di addestramento sulla console o il file train.log nella cartella dell'esperimento", "请指定说话人id": "Si prega di specificare l'ID del parlante", "请选择index文件": "Seleziona il file di indice", "请选择pth文件": "Seleziona il file pth", "请选择说话人id": "Seleziona l'ID del parlante", "转换": "<PERSON><PERSON><PERSON>", "输入实验名": "Inserisci il nome dell'esperimento", "输入待处理音频文件夹路径": "Inserisci il percorso della cartella dei file audio da elaborare", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Inserisci il percorso della cartella dei file audio da elaborare (copialo dalla barra degli indirizzi del gestore dei file)", "输入待处理音频文件路径(默认是正确格式示例)": "Inserisci il percorso del file audio da elaborare (esempio di formato corretto predefinito)", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Inserisci la proporzione di fusione della sostituzione dell'involucro del volume di ingresso con l'involucro del volume di uscita, più vicino a 1 più utilizza l'involucro di uscita", "输入监听": "Inserisci l'ascolto", "输入训练文件夹路径": "Inserisci il percorso della cartella di addestramento", "输入设备": "Dispositivo di input", "输入降噪": "Inserisci la riduzione del rumore", "输出信息": "Informazioni di output", "输出变声": "Variazione della voce in output", "输出设备": "Dispositivo di output", "输出降噪": "Riduzione del rumore in output", "输出音频(右下角三个点,点了可以下载)": "Audio in output (tre punti nell'angolo in basso a destra, fare clic per scaricare)", "选择.index文件": "Seleziona il file .index", "选择.pth文件": "Seleziona il file .pth", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "Seleziona l'algoritmo di estrazione dell'altezza del suono, l'input vocale può utilizzare pm per velocizzare, harvest ha bassi migliori ma è incredibilmente lento, crepe ha un buon effetto ma consuma molte risorse della GPU", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Seleziona l'algoritmo di estrazione dell'altezza del suono, l'input vocale può utilizzare pm per velocizzare, harvest ha bassi migliori ma è incredibilmente lento, crepe ha un buon effetto ma consuma molte risorse della GPU, rmvpe ha il miglior effetto ed è leggermente esigente sulla GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "Seleziona l'algoritmo di estrazione dell'altezza del suono: l'input vocale può utilizzare pm per velocizzare, la qualità del suono è elevata ma richiede molte risorse della CPU; l'input vocale può utilizzare dio per velocizzare, harvest ha una qualità del suono migliore ma è lento, rmvpe ha il miglior effetto ed è leggermente esigente sulla CPU/GPU", "采样率:": "Frequenza di campionamento:", "采样长度": "Lunghezza del campionamento", "重载设备列表": "Ricarica la lista dei dispositivi", "音调设置": "Impostazioni del tono", "音频设备(请使用同种类驱动)": "Dispositivo audio (usa driver della stessa categoria)", "音高算法": "Algoritmo dell'altezza del suono", "额外推理时长": "Tempo di inferenza extra"}