{"很遗憾您这没有能用的显卡来支持您训练": "残念ながら、トレーニングをサポートする利用可能なグラフィックカードがありません", "UVR5已开启": "UVR5がオンになっています", "UVR5已关闭": "UVR5がオフになっています", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "このソフトウェアはMITライセンスでオープンソース化されており、作者はソフトウェアに対して一切の制御権を持っていません。ソフトウェアを使用する者、ソフトウェアから導出される音声を広める者は、自己責任で行ってください。<br>この条件を認めない場合、ソフトウェアパッケージ内の任意のコードやファイルを使用または引用することはできません。詳細はルートディレクトリの<b>LICENSE</b>を参照してください。", "0-前置数据集获取工具": "0-データセット取得ツールの事前処理", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5ボーカルアカンパニメント分離＆リバーブおよびディレイ除去ツール", "是否开启UVR5-WebUI": "UVR5-WebUIをオンにしますか", "UVR5进程输出信息": "UVR5プロセスの出力情報", "0b-语音切分工具": "0b-音声分割ツール", ".list标注文件的路径": ".listアノテーションファイルのパス", "GPT模型列表": "GPTモデルリスト", "SoVITS模型列表": "SoVITSモデルリスト", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。": "音声を切り取った後の音声が保存されているディレクトリ！読み取られる音声ファイルの完全なパス=このディレクトリ-連結-リストファイル内の波形に対応するファイル名（フルパスではない）。", "音频自动切分输入路径，可文件可文件夹": "オーディオの自動分割入力パス、ファイルまたはフォルダを指定できます", "切分后的子音频的输出根目录": "分割後のサブオーディオの出力ルートディレクトリ", "怎么切": "どうやって切るか", "不切": "切らない", "凑四句一切": "4つの文で埋める", "按英文句号.切": "英文のピリオドで切ってください", "threshold:音量小于这个值视作静音的备选切割点": "閾値：この値未満の音量は静音と見なされ、代替のカットポイントとして扱われます", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length：各セグメントの最小長さ。最初のセグメントが短すぎる場合、連続して後続のセグメントに接続され、この値を超えるまで続きます。", "min_interval:最短切割间隔": "min_interval：最短カット間隔", "hop_size:怎么算音量曲线，越小精度越大計算量越高（不是精度越大效果越好）": "hop_size：音量曲線を計算する方法。値が小さいほど精度が高くなり、計算量が増加します（精度が高いほど効果が良いわけではありません）。", "max_sil_kept:切完后静音最多留多长": "max_sil_kept：切り終えた後、最大でどれだけ静かにするか", "开启语音切割": "音声の分割を開始", "终止语音切割": "音声の分割を停止", "max:归一化后最大值多少": "max：正規化後の最大値", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix：正規化後のオーディオが入る割合", "切割使用的进程数": "分割に使用されるプロセス数", "语音切割进程输出信息": "音声分割プロセスの出力情報", "0c-中文批量离线ASR工具": "0c-中国語バッチオフラインASRツール", "开启离线批量ASR": "オフラインバッチASRを開始", "终止ASR进程": "ASRプロセスを停止", "批量ASR(中文only)输入文件夹路径": "バッチASR（中国語のみ）の入力フォルダパス", "ASR进程输出信息": "ASRプロセスの出力情報", "0d-语音文本校对标注工具": "0d-音声テキストの校正アノテーションツール", "是否开启打标WebUI": "WebUIを使用したアノテーションを開始しますか", "打标数据标注文件路径": "アノテーションデータのファイルパス", "打标工具进程输出信息": "アノテーションツールプロセスの出力情報", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "*实验/模型名": "*実験/モデル名", "显卡信息": "グラフィックカード情報", "预训练的SoVITS-G模型路径": "事前にトレーニングされたSoVITS-Gモデルのパス", "预训练的SoVITS-D模型路径": "事前にトレーニングされたSoVITS-Dモデルのパス", "预训练的GPT模型路径": "事前にトレーニングされたGPTモデルのパス", "1A-训练集格式化工具": "1A-トレーニングデータのフォーマットツール", "输出logs/实验名目录下应有23456开头的文件和文件夹": "logs/実験名ディレクトリには23456で始まるファイルとフォルダが含まれている必要があります", "*文本标注文件": "*テキスト注釈ファイル", "*训练集音频文件目录": "*トレーニングデータのオーディオファイルディレクトリ", "训练集音频文件目录 拼接 list文件里波形对应的文件名。": "トレーニングデータのオーディオファイルディレクトリ。リストファイル内の波形に対応するファイル名を連結します。", "1Aa-文本内容": "1Aa-テキストの内容", "GPU卡号以-分割，每个卡号一个进程": "GPUカード番号はハイフンで区切り、各カード番号ごとに1つのプロセスが実行されます", "预训练的中文BERT模型路径": "事前にトレーニングされた中文BERTモデルのパス", "开启文本获取": "テキストの取得を開始", "终止文本获取进程": "テキスト取得プロセスを停止", "文本进程输出信息": "テキストプロセスの出力情報", "1Ab-SSL自监督特征提取": "1Ab-SSLセルフスーパーバイズ特徴抽出", "预训练的SSL模型路径": "事前にトレーニングされたSSLモデルのパス", "开启SSL提取": "SSL抽出を開始", "终止SSL提取进程": "SSL抽出プロセスを停止", "SSL进程输出信息": "SSLプロセスの出力情報", "1Ac-语义token提取": "1Ac-セマンティックトークン抽出", "开启语义token提取": "セマンティックトークン抽出を開始", "终止语义token提取进程": "セマンティックトークン抽出プロセスを停止", "语义token提取进程输出信息": "セマンティックトークン抽出プロセスの出力情報", "1Aabc-训练集格式化一键三连": "1Aabc-トレーニングデータのフォーマットワンクリック三連", "开启一键三连": "ワンクリック三連を開始", "终止一键三连": "ワンクリック三連を停止", "一键三连进程输出信息": "ワンクリック三連プロセスの出力情報", "1B-微调训练": "1B-ファインチューニングトレーニング", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITSトレーニング。共有用のモデルファイルはSoVITS_weightsディレクトリに出力されます。", "每张显卡的batch_size": "各グラフィックカードのバッチサイズ", "总训练轮数total_epoch，不建议太高": "総トレーニングエポック数total_epoch、高すぎないようにお勧めします", "文本模块学习率权重": "テキストモジュールの学習率の重み", "保存频率save_every_epoch": "保存頻度save_every_epoch", "是否仅保存最新的ckpt文件以节省硬盘空间": "最新のckptファイルのみを保存してディスクスペースを節約するかどうか", "是否在每次保存时间点将最终小模型保存至weights文件夹": "各保存時間点で最終的な小さなモデルをweightsフォルダに保存するかどうか", "开启SoVITS训练": "SoVITSトレーニングを開始", "终止SoVITS训练": "SoVITSトレーニングを停止", "SoVITS训练进程输出信息": "SoVITSトレーニングプロセスの出力情報", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPTトレーニング。共有用のモデルファイルはGPT_weightsディレクトリに出力されます。", "总训练轮数total_epoch": "総トレーニングエポック数total_epoch", "开启GPT训练": "GPTトレーニングを開始", "终止GPT训练": "GPTトレーニングを停止", "GPT训练进程输出信息": "GPTトレーニングプロセスの出力情報", "1C-推理": "1C-推論", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "SoVITS_weightsおよびGPT_weightsに保存されたモデルを選択します。デフォルトのものはプレトレインであり、ゼロショットTTSを体験できます。", "*GPT模型列表": "*GPTモデルリスト", "*SoVITS模型列表": "*SoVITSモデルリスト", "GPU卡号,只能填1个整数": "GPU番号、1つの整数しか入力できません", "刷新模型路径": "モデルのパスを更新", "是否开启TTS推理WebUI": "TTS推論WebUIを開く", "TTS推理WebUI进程输出信息": "TTS推論WebUIプロセスの出力情報", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-ボイスチェンジャー", "施工中，请静候佳音": "施工中、お待ちください", "TTS推理进程已开启": "TTS推論プロセスが開始されました", "TTS推理进程已关闭": "TTS推論プロセスが終了しました", "打标工具WebUI已开启": "校正ツールWebUIが開始されました", "打标工具WebUI已关闭": "校正ツールWebUIが終了しました", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. 如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录LICENSE.": "このソフトウェアはMITライセンスでオープンソース化されており、作者はソフトウェアに対して一切の制御権を持っていません。ソフトウェアを使用する者、ソフトウェアからエクスポートされた音声を伝播する者は、自己の責任を負います。この条件を受け入れない場合は、ソフトウェアパッケージ内の任意のコードやファイルを使用または引用することはできません。詳細はLICENSEを参照してください。", "*请上传并填写参考信息": "*参照情報をアップロードして記入してください", "*请填写需要合成的目标文本": "*合成が必要な対象のテキストを記入してください", "ASR任务开启：%s": "ASRタスクが開始されました：%s", "GPT训练完成": "GPTトレーニングが完了しました", "GPT训练开始：%s": "GPTトレーニングが開始されました：%s", "SSL提取进程执行中": "SSL抽出プロセス実行中", "SSL提取进程结束": "SSL抽出プロセスが終了しました", "SoVITS训练完成": "SoVITSトレーニングが完了しました", "SoVITS训练开始：%s": "SoVITSトレーニングが開始されました：%s", "一键三连中途报错": "ワンクリックフォーマット中にエラーが発生しました", "一键三连进程结束": "ワンクリックフォーマットが終了しました", "中文": "中国語", "凑50字一切": "50文字ずつカット", "凑五句一切": "5つの文ごとにカット", "切分后文本": "分割後のテキスト", "切割执行中": "オーディオの分割中", "切割结束": "オーディオの分割が完了しました", "参考音频的文本": "参照オーディオのテキスト", "参考音频的语种": "参照オーディオの言語", "合成语音": "推論を開始", "后续将支持混合语种编码文本输入。": "後で混合言語コードテキストの入力がサポートされるようになります。", "已有正在进行的ASR任务，需先终止才能开启下一次任务": "すでに進行中のASRタスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的GPT训练任务，需先终止才能开启下一次任务": "すでに進行中のGPTトレーニングタスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的SSL提取任务，需先终止才能开启下一次任务": "すでに進行中のSSL抽出タスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的SoVITS训练任务，需先终止才能开启下一次任务": "すでに進行中のSoVITSトレーニングタスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的一键三连任务，需先终止才能开启下一次任务": "すでに進行中のワンクリックフォーマットタスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的切割任务，需先终止才能开启下一次任务": "すでに進行中のオーディオの分割タスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的文本任务，需先终止才能开启下一次任务": "すでに進行中のTTS校正タスクがあります。次のタスクを開始する前に停止してください", "已有正在进行的语义token提取任务，需先终止才能开启下一次任务": "すでに進行中の意味トークン抽出タスクがあります。次のタスクを開始する前に停止してください", "已终止ASR进程": "ASRタスクが終了しました", "已终止GPT训练": "GPTトレーニングが終了しました", "已终止SoVITS训练": "SoVITSトレーニングが終了しました", "已终止所有1a进程": "すべての1aタスクが終了しました", "已终止所有1b进程": "すべての1bタスクが終了しました", "已终止所有一键三连进程": "すべてのワンクリックフォーマットタスクが終了しました", "已终止所有切割进程": "すべてのオーディオの分割タスクが終了しました", "已终止所有语义token进程": "すべての意味トークンタスクが終了しました", "按中文句号。切": "中国語の句点でカット", "文本切分工具。太长的文本合成出来效果不一定好，所以太长建议先切。合成会根据文本的换行分开合成再拼起来。": "テキストスライサーツール。長文を変換すると効果が不安定になる可能性があるため、長文の場合は事前に切り分けることをお勧めします。推論時には、テキストを個別に推論し、それを組み合わせて再構築します。", "文本进程执行中": "テキスト処理中", "文本进程结束": "テキスト処理が終了しました", "日文": "日本語", "英文": "英語", "语义token提取进程执行中": "意味トークン抽出実行中", "语义token提取进程结束": "意味トークン抽出が終了しました", "请上传参考音频": "参照オーディオをアップロードしてください", "输入路径不存在": "入力パスが存在しません", "输入路径存在但既不是文件也不是文件夹": "入力ディレクトリが存在しますが、ファイルでもフォルダでもありません", "输出的语音": "推論結果", "进度：1a-done": "進捗：1a完了", "进度：1a-done, 1b-ing": "進捗：1a完了、1b進行中", "进度：1a-ing": "進捗：1a進行中", "进度：1a1b-done": "進捗：1a1b完了", "进度：1a1b-done, 1cing": "進捗：1a1b完了、1c進行中", "进度：all-done": "進捗：all-done", "需要合成的切分前文本": "推論が必要な分割前のテキスト", "需要合成的文本": "推論テキスト", "需要合成的语种": "推論テキストの言語", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "3以上の場合：収穫音高の認識結果に中央値フィルタリングを適用します。値はフィルターの半径を表し、息遣いを減少させることができます。", "A模型权重": "モデルAの重み (w):", "A模型路径": "モデルAのパス:", "B模型路径": "モデルBのパス:", "E:\\语音音频+标注\\米津玄师\\src": "C:\\Users\\<USER>\\src", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0曲線ファイル（オプション）。1行に1つの音高があります。デフォルトのF0とピッチ変調の代わりに使用します:", "Index Rate": "インデックスレート", "Onnx导出": "Onnxエクスポート", "Onnx输出路径": "Onnxエクスポートパス:", "RVC模型路径": "RVCモデルパス:", "ckpt处理": "ckpt処理", "harvest进程数": "harvestピッチアルゴリズムに使用するCPUプロセス数", "index文件路径不可包含中文": "インデックスファイルパスには中文を含めないでください", "pth文件路径不可包含中文": "pthファイルパスには中文を含めないでください", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "異なるプロセスカードの入力に使用するGPUインデックスを'-'で区切って入力します。例：0-0-1はGPU0で2つのプロセスを実行し、GPU1で1つのプロセスを実行します", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "ステップ1：実験構成を記入します。実験データは「logs」フォルダに保存され、各実験には別々のフォルダがあります。実験名のパスを手動で入力する必要があり、実験構成、ログ、トレーニングされたモデルファイルが含まれています。", "step1:正在处理数据": "ステップ1：データ処理中", "step2:正在提取音高&正在提取特征": "ステップ2：ピッチ抽出と特徴抽出中", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "ステップ2a：トレーニングフォルダ内のデコード可能なすべてのファイルを自動的にトラバースし、スライス正規化を実行します。実験ディレクトリに2つのwavフォルダが生成されます。現時点では、単一の歌手/スピーカーのトレーニングのみがサポートされています。", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "ステップ2b：CPUを使用してピッチを抽出します（モデルにピッチがある場合）、GPUを使用して特徴を抽出します（GPUインデックスを選択します）:", "step3: 填写训练设置, 开始训练模型和索引": "ステップ3：トレーニング設定を入力し、モデルとインデックスのトレーニングを開始します", "step3a:正在训练模型": "ステップ3a：モデルのトレーニングが開始されました", "一键训练": "ワンクリックトレーニング", "也可批量输入音频文件, 二选一, 优先读文件夹": "複数のオーディオファイルもインポートできます。フォルダパスが存在する場合、この入力は無視されます。", "以-分隔输入使用的卡号, 例如 0-1-2 使用卡0和卡1和卡2": "GPUインデックスを'-'で区切って入力します。例：0-1-2はGPU 0、1、および2を使用します。", "伴奏人声分离&去混响&去回声": "ボーカル/伴奏の分離と残響の除去", "使用模型采样率": "使用するモデルのサンプルレート", "使用设备采样率": "使用デバイスのサンプルレート", "保存名": "保存名:", "保存的文件名, 默认空为和源文件同名": "保存ファイル名（デフォルト：元のファイルと同じ）:", "保存的模型名不带后缀": "保存されるモデル名（拡張子なし）:", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "清濁音と呼吸音を保護し、電子音楽の撕裂などのアーティファクトを防ぎます。0.5まで引っ張ると無効になり、保護力を高めるには値を下げますが、索引の精度が低下する可能性があります。", "修改": "変更", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "モデル情報の変更（'weights'フォルダから抽出された小さなモデルファイルのみサポート）", "停止音频转换": "オーディオ変換を停止", "全流程结束！": "すべてのプロセスが完了しました！", "刷新音色列表和索引路径": "ボイスリストとインデックスパスをリフレッシュ", "加载模型": "モデルの読み込み", "加载预训练底模D路径": "事前にトレーニングされたベースモデルDのパスをロード:", "加载预训练底模G路径": "事前にトレーニングされたベースモデルGのパスをロード:", "单次推理": "単一推論", "卸载音色省显存": "GPUメモリを節約するためにボイスをアンロード:", "变调(整数, 半音数量, 升八度12降八度-12)": "トランスポーズ（整数、半音の数、8度上げ: 12、8度下げ: -12）:", "后处理重采样至最终采样率，0为不进行重采样": "後処理でオーディオを最終のサンプルレートに再サンプリングします。リサンプリングを行わない場合は0に設定してください:", "否": "いいえ", "启用相位声码器": "位相音声コーダーを有効にする", "响应阈值": "応答閾値", "响度因子": "音量ファクター", "处理数据": "データ処理", "导出Onnx模型": "Onnxモデルのエクスポート", "导出文件格式": "エクスポートファイル形式", "常见问题解答": "よくある質問 (FAQ)", "常规设置": "一般的な設定", "开始音频转换": "オーディオ変換を開始", "性能设置": "性能設定", "批量推理": "一括推論", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "一括変換。変換するオーディオファイルが含まれるフォルダを入力するか、複数のオーディオファイルをアップロードします。変換されたオーディオは指定されたフォルダ (デフォルト: 'opt') に出力されます。", "指定输出主人声文件夹": "ボーカルの出力フォルダを指定:", "指定输出文件夹": "出力フォルダの指定:", "指定输出非主人声文件夹": "伴奏の出力フォルダを指定:", "推理时间(ms):": "推論時間 (ms):", "推理音色": "推論ボイス:", "提取": "抽出", "提取音高和处理数据使用的CPU进程数": "ピッチ抽出およびデータ処理に使用されるCPUプロセスの数:", "是": "はい", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "すべてのトレーニングセットをGPUメモリにキャッシュするかどうか。小さなデータセット (10分以下) をキャッシュするとトレーニングが高速化されますが、大きなデータセットをキャッシュするとGPUメモリが消費され、あまり速度が向上しないかもしれません:", "查看": "表示", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "モデル情報を表示します ( 'weights' フォルダから抽出された小さなモデルファイルにのみ対応):", "检索特征占比": "特徴の検索比率 (アクセントの強度を制御、高すぎるとアーティファクトが発生します):", "模型": "モデル", "模型推理": "モデル推論", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "モデル抽出 ( 'logs' フォルダ内の大きなファイルモデルのパスを入力)。トレーニングを途中で停止して手動で小さなモデルファイルを抽出および保存したい場合、または中間モデルをテストしたい場合に使用します:", "模型是否带音高指导": "モデルにピッチガイダンスがあるかどうか:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "モデルにピッチガイダンスがあるかどうか (歌唱には必須、音声にはオプション):", "模型是否带音高指导,1是0否": "モデルにピッチガイダンスがあるかどうか (1: はい、0: いいえ):", "模型版本型号": "モデルアーキテクチャバージョン:", "模型融合, 可用于测试音色融合": "モデルフュージョン、音色フュージョンをテストするために使用できます", "模型路径": "モデルへのパス:", "淡入淡出长度": "フェードの長さ", "版本": "バージョン", "特徴提取": "特徴抽出", "特徴检索库文件路径,为空则使用下拉的选择结果": "特徴インデックスファイルへのパス。空白の場合はドロップダウンから選択された結果が使用されます:", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "男性から女性への変換では+12キーが推奨され、女性から男性への変換では-12キーが推奨されます。音域が広すぎて音声が歪む場合は、適切な音域に手動で調整することもできます。", "目标采样率": "目標サンプルレート:", "算法延迟(ms):": "アルゴリズムの遅延(ms):", "自动检测index路径,下拉式选择(dropdown)": "indexパスを自動検出し、ドロップダウンから選択します:", "融合": "フュージョン", "要改的模型信息": "変更するモデル情報:", "要置入的模型信息": "挿入するモデル情報:", "训练": "トレーニング", "训练模型": "モデルのトレーニング", "训练特征索引": "特徴索引のトレーニング", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "トレーニングが完了しました。トレーニングログはコンソールまたは実験フォルダの 'train.log' ファイルで確認できます。", "请指定说话人id": "話者/歌手のIDを指定してください:", "请选择index文件": ".index ファイルを選択してください", "请选择pth文件": ".pth ファイルを選択してください", "请选择说话人id": "話者/歌手のIDを選択してください:", "转换": "変換", "输入实验名": "実験名を入力:", "输入待处理音频文件夹路径": "処理するオーディオフォルダのパスを入力してください:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "処理するオーディオフォルダのパスを入力してください (ファイルマネージャのアドレスバーからコピーしてください):", "输入待处理音频文件路径(默认是正确格式示例)": "処理するオーディオファイルのパスを入力してください (デフォルトは正しい形式の例です):", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "音量エンベロープのスケーリングを調整します。0に近いほど、元のボーカルの音量に似ます。相対的に低い値に設定すると、ノイズをマスキングし、音量がより自然に聞こえるようになります。1に近いほど、一貫して大きな音量になります:", "输入监听": "入力ボイスモニター", "输入训练文件夹路径": "トレーニングフォルダのパスを入力してください:", "输入设备": "入力デバイス", "输入降噪": "ノイズリダクションの入力", "输出信息": "出力情報", "输出变声": "変換されたボイスの出力", "输出设备": "出力デバイス", "输出降噪": "ノイズリダクションの出力", "输出音频(右下角三个点,点了可以下载)": "オーディオの出力 (右下隅の三点をクリックしてダウンロード)", "选择.index文件": ".index ファイルを選択してください", "选择.pth文件": ".pth ファイルを選択してください", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU": "音高抽出アルゴリズムを選択します。歌声を抽出する場合は 'pm' を使用して高速化できます。高品質な音声でパフォーマンスが向上するが、CPUの使用が悪化する場合は 'dio' を使用できます。 'harvest' は品質が向上しますが、遅いです。 'rmvpe' は最高の品質で、少ないGPUが必要です", "选择音高提取算法,输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢,rmvpe效果最好且微吃CPU/GPU": "音高抽出アルゴリズムを選択します。歌声を抽出する場合は 'pm' を使用して高速化できます。高品質な音声でパフォーマンスが向上するが、CPUの使用が悪化する場合は 'dio' を使用できます。 'harvest' は品質が向上しますが、遅いです。 'rmvpe' は最高の品質で、CPU/GPUの使用が少ないです", "采样率:": "サンプルレート:", "采样长度": "サンプル長", "重载设备列表": "デバイスリストを再読み込み", "音调设置": "ピッチ設定", "音频设备(请使用同种类驱动)": "オーディオデバイス (同じタイプのドライバを使用してください)", "音高算法": "音程検出アルゴリズム", "额外推理时长": "追加推論時間"}